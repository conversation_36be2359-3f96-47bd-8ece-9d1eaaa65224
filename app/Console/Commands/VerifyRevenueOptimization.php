<?php

namespace App\Console\Commands;

use App\Http\Controllers\Admin\RevenueController;
use App\Repositories\CastCalendar\CastCalendarRepository;
use App\Repositories\CastPriceLiving\CastPriceLivingRepository;
use App\Repositories\CostShop\CostShopRepository;
use App\Repositories\Course\CourseRepository;
use App\Repositories\DailyRevenue\DailyRevenueRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\PriceDecorateRoom\PriceDecorateRoomRepository;
use App\Repositories\Room\RoomCalendarRepository;
use App\Repositories\Setting\SettingRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use ReflectionClass;
use ReflectionMethod;

class VerifyRevenueOptimization extends Command
{
    protected $signature = 'verify:revenue-optimization {--month=5} {--year=2025} {--type=month}';
    protected $description = 'Verify optimized revenue logic produces identical output to original logic';

    private $revenueController;
    private $differences = [];
    private $totalFields = 0;
    private $matchedFields = 0;

    public function __construct(
        CastCalendarRepository $castCalendarRepository,
        CourseRepository $courseRepository,
        OrderRepository $orderRepository,
        RoomCalendarRepository $roomCalendarRepository,
        CostShopRepository $costShopRepository,
        CastPriceLivingRepository $castPriceLivingRepository,
        PriceDecorateRoomRepository $priceDecorateRoomRepository,
        SettingRepository $settingRepository,
        DailyRevenueRepository $dailyRevenueRepository
    ) {
        parent::__construct();
        
        $this->revenueController = new RevenueController(
            $castCalendarRepository,
            $courseRepository,
            $orderRepository,
            $roomCalendarRepository,
            $costShopRepository,
            $castPriceLivingRepository,
            $priceDecorateRoomRepository,
            $settingRepository,
            $dailyRevenueRepository
        );
    }

    public function handle()
    {
        $type = $this->option('type');
        $month = $this->option('month');
        $year = $this->option('year');

        $this->info("🔍 Verifying Revenue Optimization");
        $this->info("Type: {$type}, Period: {$year}-{$month}");
        $this->line('');

        if ($type === 'month') {
            $this->verifyMonthlyRevenue($year, $month);
        } elseif ($type === 'year') {
            $this->verifyYearlyRevenue($year);
        } else {
            $this->error('Invalid type. Use --type=month or --type=year');
            return 1;
        }

        $this->displayResults();
        return 0;
    }

    private function verifyMonthlyRevenue($year, $month)
    {
        $this->info("📊 Verifying Monthly Revenue for {$year}-{$month}");
        $this->line('');

        $request = new Request(['year' => $year, 'month' => $month]);

        // Get original data (force fallback)
        $this->info("Getting original data...");
        $originalData = $this->getOriginalData($request, 'month');

        // Get optimized data
        $this->info("Getting optimized data...");
        $optimizedData = $this->getOptimizedData($request, 'month');

        // Compare the data
        $this->compareData($originalData, $optimizedData, 'Monthly');
    }

    private function verifyYearlyRevenue($year)
    {
        $this->info("📊 Verifying Yearly Revenue for {$year}");
        $this->line('');

        $request = new Request(['year' => $year]);

        // Get original data (force fallback)
        $this->info("Getting original data...");
        $originalData = $this->getOriginalData($request, 'year');

        // Get optimized data
        $this->info("Getting optimized data...");
        $optimizedData = $this->getOptimizedData($request, 'year');

        // Compare the data
        $this->compareData($originalData, $optimizedData, 'Yearly');
    }

    private function getOriginalData($request, $type)
    {
        // Temporarily disable optimization by making hasDataForDateRange return false
        $reflection = new ReflectionClass($this->revenueController);
        $dailyRevenueRepo = $reflection->getProperty('dailyRevenueRepository');
        $dailyRevenueRepo->setAccessible(true);
        $repo = $dailyRevenueRepo->getValue($this->revenueController);

        // Mock the hasDataForDateRange method to return false (force fallback)
        $originalMethod = new ReflectionMethod($repo, 'hasDataForDateRange');
        
        // Create a temporary method that always returns false
        $tempRepo = new class($repo) {
            private $originalRepo;
            
            public function __construct($originalRepo) {
                $this->originalRepo = $originalRepo;
            }
            
            public function hasDataForDateRange($start, $end) {
                return false; // Force fallback to original logic
            }
            
            public function __call($method, $args) {
                return call_user_func_array([$this->originalRepo, $method], $args);
            }
        };

        // Replace the repository temporarily
        $dailyRevenueRepo->setValue($this->revenueController, $tempRepo);

        try {
            if ($type === 'month') {
                $response = $this->revenueController->getStatisticForMonth($request);
            } else {
                $response = $this->revenueController->getStatisticForYear($request);
            }
            
            $data = $this->extractDataFromController();
        } finally {
            // Restore original repository
            $dailyRevenueRepo->setValue($this->revenueController, $repo);
        }

        return $data;
    }

    private function getOptimizedData($request, $type)
    {
        if ($type === 'month') {
            $response = $this->revenueController->getStatisticForMonth($request);
        } else {
            $response = $this->revenueController->getStatisticForYear($request);
        }
        
        return $this->extractDataFromController();
    }

    private function extractDataFromController()
    {
        $reflection = new ReflectionClass($this->revenueController);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        
        return $dataProperty->getValue($this->revenueController);
    }

    private function compareData($originalData, $optimizedData, $type)
    {
        $this->info("🔍 Comparing {$type} Data...");
        $this->line('');

        // Compare totalStatistic
        $this->compareSection('totalStatistic', $originalData['totalStatistic'] ?? [], $optimizedData['totalStatistic'] ?? []);
        
        // Compare other key fields
        $keyFields = [
            'totalCustomer',
            'totalRevenueMonth',
            'chartRevenue',
            'courses',
            'arrDate'
        ];

        foreach ($keyFields as $field) {
            $this->compareField($field, $originalData[$field] ?? null, $optimizedData[$field] ?? null);
        }

        // Compare revenueGroupDate
        $this->compareRevenueGroupDate($originalData['revenueGroupDate'] ?? [], $optimizedData['revenueGroupDate'] ?? []);
        
        // Compare revenueCourseGroupDate
        $this->compareRevenueCourseGroupDate($originalData['revenueCourseGroupDate'] ?? [], $optimizedData['revenueCourseGroupDate'] ?? []);
        
        // Compare priceCastCalendarGroupDate
        $this->comparePriceCastCalendarGroupDate($originalData['priceCastCalendarGroupDate'] ?? [], $optimizedData['priceCastCalendarGroupDate'] ?? []);
    }

    private function compareSection($sectionName, $original, $optimized)
    {
        $this->info("📋 Comparing {$sectionName}:");
        
        if (!is_array($original) || !is_array($optimized)) {
            $this->compareField($sectionName, $original, $optimized);
            return;
        }

        foreach ($original as $key => $value) {
            $this->compareField("{$sectionName}.{$key}", $value, $optimized[$key] ?? null);
        }

        // Check for extra fields in optimized
        foreach ($optimized as $key => $value) {
            if (!isset($original[$key])) {
                $this->warn("Extra field in optimized: {$sectionName}.{$key}");
            }
        }
    }

    private function compareField($fieldName, $original, $optimized)
    {
        $this->totalFields++;

        if ($this->valuesMatch($original, $optimized)) {
            $this->matchedFields++;
            $this->line("✅ {$fieldName}: MATCH");
        } else {
            $this->differences[] = [
                'field' => $fieldName,
                'original' => $original,
                'optimized' => $optimized
            ];
            $this->error("❌ {$fieldName}: MISMATCH");
            $this->line("   Original:  " . $this->formatValue($original));
            $this->line("   Optimized: " . $this->formatValue($optimized));
        }
    }

    private function valuesMatch($original, $optimized)
    {
        // Handle numeric comparisons with small tolerance for floating point
        if (is_numeric($original) && is_numeric($optimized)) {
            return abs($original - $optimized) < 0.01;
        }

        // Handle arrays/collections
        if (is_array($original) && is_array($optimized)) {
            return $this->arraysMatch($original, $optimized);
        }

        // Handle objects
        if (is_object($original) && is_object($optimized)) {
            return $this->objectsMatch($original, $optimized);
        }

        // Direct comparison
        return $original === $optimized;
    }

    private function arraysMatch($arr1, $arr2)
    {
        if (count($arr1) !== count($arr2)) {
            return false;
        }

        foreach ($arr1 as $key => $value) {
            if (!isset($arr2[$key]) || !$this->valuesMatch($value, $arr2[$key])) {
                return false;
            }
        }

        return true;
    }

    private function objectsMatch($obj1, $obj2)
    {
        $arr1 = (array) $obj1;
        $arr2 = (array) $obj2;
        return $this->arraysMatch($arr1, $arr2);
    }

    private function formatValue($value)
    {
        if (is_null($value)) {
            return 'NULL';
        }
        if (is_bool($value)) {
            return $value ? 'TRUE' : 'FALSE';
        }
        if (is_numeric($value)) {
            return number_format($value, 2);
        }
        if (is_array($value)) {
            return 'Array[' . count($value) . ']';
        }
        if (is_object($value)) {
            return get_class($value) . ' Object';
        }
        return (string) $value;
    }

    private function compareRevenueGroupDate($original, $optimized)
    {
        $this->info("📋 Comparing revenueGroupDate:");
        
        if (empty($original) && empty($optimized)) {
            $this->line("✅ Both empty - MATCH");
            return;
        }

        foreach ($original as $date => $originalRevenue) {
            if (!isset($optimized[$date])) {
                $this->error("❌ Missing date in optimized: {$date}");
                continue;
            }

            $optimizedRevenue = $optimized[$date];
            $revenueFields = [
                'total_price_cash_orders',
                'total_price_cast',
                'price_orders_cash',
                'price_paid_for_cast',
                'price_dormitory',
                'price_expenses',
                'price_living',
                'price_decorate_room',
                'total_extension',
                'total_option',
                'total_customers',
                'total_price_coupon'
            ];

            foreach ($revenueFields as $field) {
                $this->compareField("revenueGroupDate[{$date}].{$field}", 
                    $originalRevenue->$field ?? null, 
                    $optimizedRevenue->$field ?? null);
            }
        }
    }

    private function compareRevenueCourseGroupDate($original, $optimized)
    {
        $this->info("📋 Comparing revenueCourseGroupDate:");
        $this->compareField('revenueCourseGroupDate.count', count($original), count($optimized));
    }

    private function comparePriceCastCalendarGroupDate($original, $optimized)
    {
        $this->info("📋 Comparing priceCastCalendarGroupDate:");
        $this->compareField('priceCastCalendarGroupDate.count', count($original), count($optimized));
    }

    private function displayResults()
    {
        $this->line('');
        $this->info("📊 VERIFICATION RESULTS");
        $this->info("======================");
        
        $matchPercentage = $this->totalFields > 0 ? ($this->matchedFields / $this->totalFields) * 100 : 0;
        
        $this->info("Total Fields Compared: {$this->totalFields}");
        $this->info("Matched Fields: {$this->matchedFields}");
        $this->info("Mismatched Fields: " . (count($this->differences)));
        $this->info("Match Percentage: " . number_format($matchPercentage, 2) . "%");
        
        if (empty($this->differences)) {
            $this->info("🎉 ALL FIELDS MATCH! Optimization is working correctly.");
        } else {
            $this->error("⚠️  Found " . count($this->differences) . " mismatches:");
            foreach ($this->differences as $diff) {
                $this->line("  - {$diff['field']}");
            }
        }
    }
}
