<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Repositories\DailyRevenue\DailyRevenueRepository;
use Carbon\Carbon;
use Exception;

class SyncDailyRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revenue:sync-daily {date?} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync daily revenue data for a specific date (defaults to yesterday)';

    protected $dailyRevenueRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(DailyRevenueRepository $dailyRevenueRepository)
    {
        parent::__construct();
        $this->dailyRevenueRepository = $dailyRevenueRepository;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            // Determine the date to sync
            $dateInput = $this->argument('date');
            if ($dateInput) {
                $date = Carbon::parse($dateInput);
            } else {
                // Default to yesterday
                $date = Carbon::yesterday();
            }

            $this->info("Starting revenue sync for date: {$date->toDateString()}");

            // Check if data already exists and force flag
            $existingData = $this->dailyRevenueRepository->getByDate($date);
            if ($existingData && !$this->option('force')) {
                $this->warn("Revenue data already exists for {$date->toDateString()}. Use --force to overwrite.");
                return 1;
            }

            // Sync the revenue data
            $result = $this->dailyRevenueRepository->syncRevenueForDate($date);

            if ($result) {
                $this->info("Successfully synced revenue data for {$date->toDateString()}");
                $this->info("Total Revenue: ¥" . number_format($result->total_revenue));
                $this->info("Total Orders: " . $result->total_orders_count);
                $this->info("Total Customers: " . $result->total_customers);
                return 0;
            } else {
                $this->error("Failed to sync revenue data for {$date->toDateString()}");
                return 1;
            }

        } catch (Exception $e) {
            $this->error("Error syncing revenue data: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
