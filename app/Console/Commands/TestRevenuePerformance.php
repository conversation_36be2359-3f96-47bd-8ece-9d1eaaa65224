<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Admin\RevenueController;
use App\Repositories\DailyRevenue\DailyRevenueRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class TestRevenuePerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revenue:test-performance {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test performance comparison between legacy and optimized revenue methods';

    protected $dailyRevenueRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(DailyRevenueRepository $dailyRevenueRepository)
    {
        parent::__construct();
        $this->dailyRevenueRepository = $dailyRevenueRepository;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = $this->argument('date') ? Carbon::parse($this->argument('date')) : Carbon::parse('2024-06-15');

        $this->info("Testing performance for date: {$date->toDateString()}");
        $this->line('');

        // Test single day performance
        $this->testSingleDayPerformance($date);

        // Test monthly performance
        $this->testMonthlyPerformance($date);

        return 0;
    }

    protected function testSingleDayPerformance($date)
    {
        $this->info('=== Single Day Performance Test ===');

        // Test optimized method
        $startTime = microtime(true);
        $queryCount = DB::getQueryLog();
        DB::enableQueryLog();

        $optimizedResult = $this->dailyRevenueRepository->getTotalStatisticByTimeOptimized($date, $date);

        $optimizedQueries = count(DB::getQueryLog()) - count($queryCount);
        $optimizedTime = (microtime(true) - $startTime) * 1000;

        DB::disableQueryLog();

        // Test legacy method (we'll need to create a controller instance)
        $controller = app(RevenueController::class);

        $startTime = microtime(true);
        DB::enableQueryLog();

        $legacyResult = $controller->getTotalStatisticByTimeLegacy($date, $date);

        $legacyQueries = count(DB::getQueryLog()) - $optimizedQueries;
        $legacyTime = (microtime(true) - $startTime) * 1000;

        DB::disableQueryLog();

        // Display results
        $this->table(['Method', 'Time (ms)', 'Queries', 'Total Revenue'], [
            ['Optimized', number_format($optimizedTime, 2), $optimizedQueries, '¥' . number_format($optimizedResult['totalRevenue'] ?? 0)],
            ['Legacy', number_format($legacyTime, 2), $legacyQueries, '¥' . number_format($legacyResult['totalRevenue'] ?? 0)]
        ]);

        $improvement = $legacyTime > 0 ? (($legacyTime - $optimizedTime) / $legacyTime) * 100 : 0;
        $this->info("Performance improvement: " . number_format($improvement, 1) . "%");
        $this->line('');
    }

    protected function testMonthlyPerformance($date)
    {
        $this->info('=== Monthly Performance Test ===');

        $startOfMonth = $date->copy()->startOfMonth();
        $endOfMonth = $date->copy()->endOfMonth();

        // Test optimized method
        $startTime = microtime(true);
        DB::enableQueryLog();

        $optimizedResult = $this->dailyRevenueRepository->getMonthlyData($date->year, $date->month);

        $optimizedQueries = count(DB::getQueryLog());
        $optimizedTime = (microtime(true) - $startTime) * 1000;

        DB::disableQueryLog();

        // Test legacy method
        $controller = app(RevenueController::class);

        $startTime = microtime(true);
        DB::enableQueryLog();

        $legacyResult = $controller->getTotalStatisticByTimeLegacy($startOfMonth, $endOfMonth);

        $legacyQueries = count(DB::getQueryLog()) - $optimizedQueries;
        $legacyTime = (microtime(true) - $startTime) * 1000;

        DB::disableQueryLog();

        // Display results
        $this->table(['Method', 'Time (ms)', 'Queries', 'Total Revenue'], [
            ['Optimized', number_format($optimizedTime, 2), $optimizedQueries, '¥' . number_format($optimizedResult->total_revenue ?? 0)],
            ['Legacy', number_format($legacyTime, 2), $legacyQueries, '¥' . number_format($legacyResult['totalRevenue'] ?? 0)]
        ]);

        $improvement = $legacyTime > 0 ? (($legacyTime - $optimizedTime) / $legacyTime) * 100 : 0;
        $this->info("Performance improvement: " . number_format($improvement, 1) . "%");
        $this->line('');
    }
}
