<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Repositories\DailyRevenue\DailyRevenueRepository;
use Carbon\Carbon;
use Exception;

class MigrateHistoricalRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revenue:migrate-historical {start-date} {end-date} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate historical revenue data to daily_revenues table for a date range';

    protected $dailyRevenueRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(DailyRevenueRepository $dailyRevenueRepository)
    {
        parent::__construct();
        $this->dailyRevenueRepository = $dailyRevenueRepository;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $startDate = Carbon::parse($this->argument('start-date'));
            $endDate = Carbon::parse($this->argument('end-date'));

            if ($startDate > $endDate) {
                $this->error('Start date must be before end date');
                return 1;
            }

            $this->info("Migrating historical revenue data from {$startDate->toDateString()} to {$endDate->toDateString()}");

            $currentDate = clone $startDate;
            $successCount = 0;
            $errorCount = 0;

            $progressBar = $this->output->createProgressBar($startDate->diffInDays($endDate) + 1);
            $progressBar->start();

            while ($currentDate <= $endDate) {
                try {
                    // Check if data already exists
                    $existingData = $this->dailyRevenueRepository->getByDate($currentDate);
                    if ($existingData && !$this->option('force')) {
                        $this->line("\nSkipping {$currentDate->toDateString()} - data already exists");
                    } else {
                        $result = $this->dailyRevenueRepository->syncRevenueForDate($currentDate);
                        if ($result) {
                            $successCount++;
                        }
                    }
                } catch (Exception $e) {
                    $this->line("\nError processing {$currentDate->toDateString()}: " . $e->getMessage());
                    $errorCount++;
                }

                $progressBar->advance();
                $currentDate->addDay();
            }

            $progressBar->finish();

            $this->line('');
            $this->info("Migration completed!");
            $this->info("Successfully processed: {$successCount} days");
            if ($errorCount > 0) {
                $this->warn("Errors encountered: {$errorCount} days");
            }

            return $errorCount > 0 ? 1 : 0;

        } catch (Exception $e) {
            $this->error("Error during migration: " . $e->getMessage());
            return 1;
        }
    }
}
