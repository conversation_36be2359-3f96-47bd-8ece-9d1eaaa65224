<?php

namespace App\Repositories\DailyRevenue;

use App\Repositories\RepositoryInterface;
use Carbon\Carbon;

interface DailyRevenueRepositoryInterface extends RepositoryInterface
{
    /**
     * Get daily revenue data for a specific date
     *
     * @param Carbon|string $date
     * @return object|null
     */
    public function getByDate($date);

    /**
     * Get daily revenue data for a date range
     *
     * @param Carbon|string $startDate
     * @param Carbon|string $endDate
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByDateRange($startDate, $endDate);

    /**
     * Get aggregated monthly data
     *
     * @param int $year
     * @param int $month
     * @return object|null
     */
    public function getMonthlyData($year, $month);

    /**
     * Get aggregated yearly data grouped by month
     *
     * @param int $year
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getYearlyData($year);

    /**
     * Create or update daily revenue record
     *
     * @param array $data
     * @return object
     */
    public function createOrUpdate(array $data);

    /**
     * Sync revenue data for a specific date
     *
     * @param Carbon|string $date
     * @return object
     */
    public function syncRevenueForDate($date);

    /**
     * Get total statistics for a date range (optimized version)
     *
     * @param Carbon|string $startDate
     * @param Carbon|string $endDate
     * @return array
     */
    public function getTotalStatisticByTimeOptimized($startDate, $endDate);
}
