<?php

namespace App\Repositories\DailyRevenue;

use App\Models\DailyRevenue;
use App\Repositories\BaseRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\CastCalendar\CastCalendarRepository;
use App\Repositories\CastPriceLiving\CastPriceLivingRepository;
use App\Repositories\CostShop\CostShopRepository;
use App\Repositories\PriceDecorateRoom\PriceDecorateRoomRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DailyRevenueRepository extends BaseRepository implements DailyRevenueRepositoryInterface
{
    protected $orderRepository;
    protected $castCalendarRepository;
    protected $castPriceLivingRepository;
    protected $costShopRepository;
    protected $priceDecorateRoomRepository;

    public function __construct(
        OrderRepository $orderRepository,
        CastCalendarRepository $castCalendarRepository,
        CastPriceLivingRepository $castPriceLivingRepository,
        CostShopRepository $costShopRepository,
        PriceDecorateRoomRepository $priceDecorateRoomRepository
    ) {
        $this->orderRepository = $orderRepository;
        $this->castCalendarRepository = $castCalendarRepository;
        $this->castPriceLivingRepository = $castPriceLivingRepository;
        $this->costShopRepository = $costShopRepository;
        $this->priceDecorateRoomRepository = $priceDecorateRoomRepository;
        parent::__construct();
    }

    public function getModel()
    {
        return DailyRevenue::class;
    }

    public function getByDate($date)
    {
        if ($date instanceof Carbon) {
            $date = $date->toDateString();
        }
        
        return $this->model->where('date', $date)->first();
    }

    public function getByDateRange($startDate, $endDate)
    {
        if ($startDate instanceof Carbon) {
            $startDate = $startDate->toDateString();
        }
        if ($endDate instanceof Carbon) {
            $endDate = $endDate->toDateString();
        }
        
        return $this->model->whereBetween('date', [$startDate, $endDate])
                          ->orderBy('date')
                          ->get();
    }

    public function getMonthlyData($year, $month)
    {
        return DailyRevenue::getMonthlyData($year, $month);
    }

    public function getYearlyData($year)
    {
        return DailyRevenue::getYearlyData($year);
    }

    public function createOrUpdate(array $data)
    {
        return $this->model->updateOrCreate(
            ['date' => $data['date']],
            $data
        );
    }

    public function syncRevenueForDate($date)
    {
        if ($date instanceof Carbon) {
            $dateCarbon = $date;
            $dateString = $date->toDateString();
        } else {
            $dateCarbon = Carbon::parse($date);
            $dateString = $date;
        }

        Log::info("Starting revenue sync for date: {$dateString}");

        try {
            // Get all revenue data for the date
            $revenueData = $this->calculateRevenueForDate($dateCarbon);
            
            // Create or update the daily revenue record
            $dailyRevenue = $this->createOrUpdate($revenueData);
            
            Log::info("Successfully synced revenue for date: {$dateString}");
            
            return $dailyRevenue;
        } catch (\Exception $e) {
            Log::error("Failed to sync revenue for date: {$dateString}. Error: " . $e->getMessage());
            throw $e;
        }
    }

    protected function calculateRevenueForDate(Carbon $date)
    {
        $dateString = $date->toDateString();
        
        // Get order statistics
        $totalCustomer = $this->orderRepository->getTotalCustomerPresentByTime($date, $date);
        $totalRevenue = $this->orderRepository->getTotalRevenuePresentByTime($date, $date);
        
        // Get cast calendar data
        $castCalendarData = $this->castCalendarRepository->getTotalPriceCastPaiedByTime($date, $date);
        
        // Get card payment data
        $cardPaymentData = $this->orderRepository->totalOrdersByCardByTime($dateString, $dateString);
        $cardPaymentInDay15 = $this->orderRepository->totalOrdersByCardInDay15ByTime($date, $date);
        
        // Get cash payment data
        $cashPaymentData = $this->orderRepository->totalOrdersByCashByTime($dateString, $dateString);
        
        // Get other revenue sources
        $livingPrice = $this->castPriceLivingRepository->totalPriceByTime($dateString, $dateString);
        $decorateRoomPrice = $this->priceDecorateRoomRepository->getTotalPriceByTime($date, $date);
        $costShop = $this->costShopRepository->totalPriceByTime($dateString, $dateString);
        
        // Get expenses
        $expensesData = $this->orderRepository->getTotalPriceExpensesOrdersPaid($date, $date);
        $expenses = isset($expensesData[0]->total_price_expenses) ? $expensesData[0]->total_price_expenses : 0;
        
        // Get course breakdown
        $courseRevenue = $this->orderRepository->getCourseOrderGroupDate($date, $date, 'date');
        $revenueByCourse = [];
        foreach ($courseRevenue as $course) {
            $revenueByCourse[$course->course_id] = $course->count_course;
        }
        
        // Get type fee breakdown
        $typeFeeRevenue = $this->orderRepository->getTotalOrderTypeFee($date, true);
        $revenueByTypeFee = [];
        foreach ($typeFeeRevenue as $typeFee) {
            $revenueByTypeFee[$typeFee->type_fee] = $typeFee->total;
        }

        return [
            'date' => $dateString,
            'total_orders_count' => $this->orderRepository->countOrdersByDate($date),
            'total_revenue' => $totalRevenue ?? 0,
            'total_cash_revenue' => $cashPaymentData['total_price'] ?? 0,
            'total_card_revenue' => $cardPaymentData['total_price'] ?? 0,
            'total_cast_payments' => $this->orderRepository->totalPriceForCastOrderByTime($dateString, $dateString) ?? 0,
            'total_dormitory_price' => $castCalendarData->total_dormitory_price ?? 0,
            'total_expenses' => $expenses ?? 0,
            'price_for_cast' => $castCalendarData->price_for_cast ?? 0,
            'total_living_price' => $livingPrice ?? 0,
            'total_decorate_room_price' => $decorateRoomPrice ?? 0,
            'total_cost_shop' => $costShop ?? 0,
            'total_customers' => $totalCustomer ?? 0,
            'card_orders_count' => $cardPaymentData['total'] ?? 0,
            'card_orders_total_price' => $cardPaymentData['total_price'] ?? 0,
            'card_orders_tax_price' => $cardPaymentData['total_tax_price'] ?? 0,
            'card_orders_cast_price' => $cardPaymentData['total_price_cast'] ?? 0,
            'card_orders_in_day15_count' => $cardPaymentInDay15['total'] ?? 0,
            'card_orders_in_day15_total_price' => $cardPaymentInDay15['total_price'] ?? 0,
            'card_orders_in_day15_tax_price' => $cardPaymentInDay15['total_tax_price'] ?? 0,
            'card_orders_in_day15_cast_price' => $cardPaymentInDay15['total_price_cast'] ?? 0,
            'card_orders_last_month_count' => ($cardPaymentData['total'] ?? 0) - ($cardPaymentInDay15['total'] ?? 0),
            'card_orders_last_month_total_price' => ($cardPaymentData['total_price'] ?? 0) - ($cardPaymentInDay15['total_price'] ?? 0),
            'card_orders_last_month_tax_price' => ($cardPaymentData['total_tax_price'] ?? 0) - ($cardPaymentInDay15['total_tax_price'] ?? 0),
            'card_orders_last_month_cast_price' => ($cardPaymentData['total_price_cast'] ?? 0) - ($cardPaymentInDay15['total_price_cast'] ?? 0),
            'cash_orders_total_price' => $cashPaymentData['total_price'] ?? 0,
            'cash_orders_cast_not_support' => $cashPaymentData['total_price_cast_not_support'] ?? 0,
            'cash_orders_support_price' => $cashPaymentData['total_price_support'] ?? 0,
            'revenue_by_course' => $revenueByCourse,
            'revenue_by_type_fee' => $revenueByTypeFee,
        ];
    }

    public function getTotalStatisticByTimeOptimized($startDate, $endDate)
    {
        dd(123);
        if ($startDate instanceof Carbon) {
            $startDate = $startDate->toDateString();
        }
        if ($endDate instanceof Carbon) {
            $endDate = $endDate->toDateString();
        }

        $data = $this->model->whereBetween('date', [$startDate, $endDate])
                           ->selectRaw('
                               SUM(total_dormitory_price) as dormitoryPrice,
                               SUM(total_expenses) as expenses,
                               SUM(price_for_cast) as priceForCast,
                               SUM(total_living_price) as priceLiving,
                               SUM(total_decorate_room_price) as priceDecorateRoom,
                               SUM(total_cost_shop) as costShop,
                               SUM(card_orders_count) as numberOrdersByCard,
                               SUM(card_orders_total_price) as priceOrdersByCard,
                               SUM(card_orders_tax_price) as taxPriceOrdersByCard,
                               SUM(card_orders_cast_price) as priceCastOrdersByCard,
                               SUM(card_orders_in_day15_count) as numberOrdersByCardInDay15,
                               SUM(card_orders_in_day15_total_price) as totalPriceOrdersByCardInDay15,
                               SUM(card_orders_in_day15_tax_price) as taxPriceOrdersByCardInDay15,
                               SUM(card_orders_in_day15_cast_price) as priceCastOrdersByCardInDay15,
                               SUM(card_orders_last_month_count) as numberOrdersByCardInLastMonth,
                               SUM(card_orders_last_month_total_price) as totalPriceOrdersByCardInLastMonth,
                               SUM(card_orders_last_month_tax_price) as taxPriceCastOrdersByCardInLastMonth,
                               SUM(card_orders_last_month_cast_price) as priceCastOrdersByCardInLastMonth,
                               SUM(cash_orders_total_price) as priceOrdersByCash,
                               SUM(cash_orders_cast_not_support) as priceCastNotSupportByCash,
                               SUM(cash_orders_support_price) as priceSupportByCash,
                               SUM(total_cast_payments) as priceOrderForCast
                           ')
                           ->first();

        if (!$data) {
            // Return default structure if no data found
            return [
                'dormitoryPrice' => 0,
                'expenses' => 0,
                'priceForCast' => 0,
                'priceLiving' => 0,
                'priceDecorateRoom' => 0,
                'costShop' => 0,
                'numberOrdersByCard' => 0,
                'priceOrdersByCard' => 0,
                'taxPriceOrdersByCard' => 0,
                'priceCastOrdersByCard' => 0,
                'numberOrdersByCardInDay15' => 0,
                'totalPriceOrdersByCardInDay15' => 0,
                'taxPriceOrdersByCardInDay15' => 0,
                'priceCastOrdersByCardInDay15' => 0,
                'numberOrdersByCardInLastMonth' => 0,
                'totalPriceOrdersByCardInLastMonth' => 0,
                'taxPriceCastOrdersByCardInLastMonth' => 0,
                'priceCastOrdersByCardInLastMonth' => 0,
                'priceOrdersByCash' => 0,
                'priceCastNotSupportByCash' => 0,
                'priceSupportByCash' => 0,
                'priceOrderForCast' => 0,
                'totalRevenue' => 0,
            ];
        }

        // Calculate total revenue
        $data->totalRevenue = $data->dormitoryPrice + $data->expenses + $data->priceLiving + 
                             $data->priceDecorateRoom - $data->priceCastNotSupportByCash - 
                             $data->priceSupportByCash - $data->costShop;

        return $data->toArray();
    }
}
