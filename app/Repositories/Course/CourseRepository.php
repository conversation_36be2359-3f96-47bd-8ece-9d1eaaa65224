<?php

namespace App\Repositories\Course;

use Exception;
use Illuminate\Support\Facades\DB;

use App\Models\Course;
use App\Models\Order;
use App\Repositories\BaseRepository;
use Carbon\Carbon;

class CourseRepository extends BaseRepository implements CourseRepositoryInterface
{
    protected $perPage = 50;

    const TIMEADD = 5;

    public function getModel()
    {
        return Course::class;
    }

    public function getCourse()
    {
        $courses = $this->model->whereNull('deleted_at')->orderBy('ordinal_number', 'ASC')->get();

        return $courses;
    }

    public function getOrdinalNumber()
    {
        $courseOrdinalNumberMax = $this->model->orderBy('ordinal_number', 'desc')->first();
        if ($courseOrdinalNumberMax) {
            $number = $courseOrdinalNumberMax->ordinal_number + 1;
            return $number;
        } else {
            return 1;
        }
    }

    public function getLastUpdateDate()
    {
        return $this->model->select('updated_at')->orderBy('updated_at', 'desc')->first();
    }

    public function reOrderCourse($id, $type)
    {
        DB::beginTransaction();
        try {
            $course = $this->model->find($id);
            if (!$course) {
                return 0;
            }
            if ($type == 'up') {
                $courseReplace = $this->model->where('ordinal_number', '<', $course->ordinal_number)->orderByDesc('ordinal_number')->first();
            } else {
                $courseReplace = $this->model->where('ordinal_number', '>', $course->ordinal_number)->orderBy('ordinal_number')->first();
            }
            if ($courseReplace) {
                $ordinalNumberReplace = $courseReplace->ordinal_number;
                $courseReplace->ordinal_number = $course->ordinal_number;
                $courseReplace->save();
                $course->ordinal_number = $ordinalNumberReplace;
                $course->save();
            } else {
                return 0;
            }
            DB::commit();
            return 1;
        } catch (Exception $e) {
            DB::rollBack();

            throw new Exception($e->getMessage());
        }
    }

    public function getAll()
    {
        $courses = $this->model->orderBy('time')->get();
        return $courses;
    }

    public function getListCousePublic($date)
    {
        return $this->model->whereNotNull('date_public')
            ->where('date_public', '<=', $date)
            ->where('status', Course::STATUS_ACTIVE)
            ->orderBy('ordinal_number', 'ASC')
            ->get();
    }

    public function getTimeCourse()
    {
        $course = $this->model->orderBy('time')->pluck('time')->all();
        return $course;
    }

    public function getTimeCourseSuggest($time, $timeEnd)
    {
        $courses = $this->getTimeCourse();
        $courseMatch = array_filter($courses, function ($val) use ($time) {
            return $val <= $time;
        });
        $timeCourseSuggest = [];
        $timeEnd = $timeEnd->format('Y-m-d H:i:s');
        if ($courseMatch) {
            foreach ($courseMatch as $course) {
                array_push($timeCourseSuggest, Carbon::create($timeEnd)->addMinute($course + $this::TIMEADD)->format('Y-m-d H:i:s'));
            }
        }
        return $timeCourseSuggest;
    }

    public function getListCoursePublic($date, $apply = false)
    {
        if ($apply) {
            return $this->model->where('apply_in_store', Course::APPLY_IN_STORE)
                ->whereNotNull('date_public')
                ->where('date_public', '<=', $date)
                ->where('status', Course::STATUS_ACTIVE)
                ->orderBy('time')
                ->get();
        }
        return $this->model->whereNotNull('date_public')
            ->where('date_public', '<=', $date)
            ->where('status', Course::STATUS_ACTIVE)
            ->orderBy('time')
            ->get();
    }

    public function getPriceCastEditTypeFee($course, $typeFee, $isFirst)
    {
        if ($typeFee == Order::TYPE_APPOINT) {
            return $course->price_cast_no_first;
        }
        if ($typeFee == Order::TYPE_NET || $typeFee == Order::TYPE_PANEL) {
            if ($isFirst == Order::MANY_TIME) {
                return $course->price_cast_no_first;
            }
            return $course->price_cast;
        }

        return $isFirst = Order::FIRST_TIME ? $course->price_cast_free : $course->price_cast_no_first;
    }

    public function updatePriceCast($courseNew, $typeFeeNew, $isFirst, $priceCastOption)
    {
        $priceCast = $this->getPriceCastEditTypeFee($courseNew, $typeFeeNew, $isFirst);
        $priceCastNew = $priceCast + $priceCastOption;
        return $priceCastNew;
    }

    public function findCourseWithTrashed($courseId)
    {
        return $this->model::withTrashed()->find($courseId);
    }
}
