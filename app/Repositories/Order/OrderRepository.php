<?php

namespace App\Repositories\Order;

use App\Models\CastCalendar;
use App\Constants\Orders;
use App\Models\Cast;
use App\Models\Coupon;
use App\Models\CouponCourse;
use App\Models\Course;
use App\Models\Customer;
use App\Models\Option;
use App\Models\Order;
use App\Models\OrderCoupon;
use App\Models\OrderOption;
use App\Models\Room;
use App\Models\SettingPriceSupport;
use App\Repositories\BaseRepository;
use App\Services\OrderService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class OrderRepository extends BaseRepository implements OrderRepositoryInterface
{
    public $methodFree = Order::METHOD_FREE;

    public function getModel()
    {
        return Order::class;
    }

    public function getListOrdersByDate($date)
    {
        return $this->model->with('cast')->with('coupons')->with('options')
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->whereDate('date', $date->format('Y-m-d'))
            ->get();
    }

    public function getListOrders($filters, $arrStatus = [])
    {
        $orders = $this->model;

        if (isset($filters['keySearch'])) {
            $orders = $orders->where('phone', 'like', '%' . trim($filters['keySearch']) . '%')->orWhere('name', 'like', '%' . $filters['keySearch'] . '%');
        }

        $orders = $orders->whereYear('actual_date_start', trim($filters['year']))
            ->whereMonth('actual_date_start', trim($filters['month']));

        if ($arrStatus) {
            $orders = $orders->whereIn('status', $arrStatus);
        }

        $orders = $orders->orderBy('id')->paginate($filters['perPage']);

        return $orders;
    }

    public function getOrders($filters, $id, $arrStatus = [])
    {
        $orders = $this->model->where('customer_id', $id);

        if ($filters['year']) {
            $orders = $orders->whereYear('actual_date_start', trim($filters['year']));
        }
        if ($filters['month']) {
            $orders = $orders->whereMonth('actual_date_start', trim($filters['month']));
        }

        if ($arrStatus) {
            $orders = $orders->whereIn('status', $arrStatus);
        }

        $orders = $orders->orderBy('id')->paginate(20);

        return $orders;
    }

    public function getTotalPriceInMonth($filters, $id = NULL, $arrStatus = [])
    {
        $totalPrice = $this->model;

        if ($id) {
            $totalPrice = $totalPrice->where('customer_id', $id);
        }

        if (isset($filters['keySearch'])) {
            $totalPrice = $totalPrice->where('phone', 'like', '%' . trim($filters['keySearch']) . '%')->orWhere('name', 'like', '%' . $filters['keySearch'] . '%');
        }

        $totalPrice = $totalPrice->whereYear('actual_date_start', trim($filters['year']))
            ->whereMonth('actual_date_start', trim($filters['month']))
            ->where('is_pay', Order::PAID);

        if ($arrStatus) {
            $totalPrice = $totalPrice->whereIn('status', $arrStatus);
        }

        $totalPrice = $totalPrice->sum('total_price');

        return $totalPrice;
    }

    public function getTotalPrice($id, $date, $arrStatus = [])
    {
        $totalPrice = $this->model->where('customer_id', $id)
            ->whereDate('date', '>=', $date)
            ->where('is_pay', Order::PAID);

        if ($arrStatus) {
            $totalPrice = $totalPrice->whereIn('status', $arrStatus);
        }

        $totalPrice = $totalPrice->sum('total_price');

        return $totalPrice;
    }

    public function getListDateOrder($castId)
    {
        $listDate = $this->model->where('cast_id', $castId)->get();
        if ($listDate) {
            return $listDate;
        }
        return false;
    }

    public function getTimeOrder($castId, $dateChoose, $dateChooseEnd, $breakTime, $orderId = null, $arrMethod = [])
    {
        $listTime = [];
        $date = clone $dateChoose;
        if ($date->hour < CLOSE_HOURS - 24) {
            $date->subDay();
        }
        $listOrder = $this->model->select('orders.*')
            ->where('cast_id', $castId)
            ->whereDate('date', $date->format('Y-m-d'))
            ->where("orders.status", '<>', Order::STATUS_CANCEL)
            ->whereNull('deleted_at');
        if ($orderId) {
            $listOrder = $listOrder->where('orders.id', '<>', $orderId);
        }
        if ($arrMethod) {
            $listOrder = $listOrder->whereIn('method', $arrMethod);
        }
        $listOrder = $listOrder->orderBy('orders.date_start')->get();
        foreach ($listOrder as $order) {
            $timeChoose = clone $dateChoose;
            $timeChooseEnd = clone $dateChooseEnd;
            $minuteExtension = $order->minute_extension ? $order->minute_extension : 0;
            if ($order->actual_date_start) {
                $timeStart = Carbon::parse($order->actual_date_start);
            } else {
                $timeStart = Carbon::parse($order->date_start);
            }
            if ($order->actual_date_end) {
                $timeEnd = Carbon::parse($order->actual_date_end);
            } else {
                $timeEnd = Carbon::parse($order->date_end)->addMinutes($minuteExtension);
            }
            if ($timeStart < $timeChooseEnd->addMinutes($breakTime) && $timeEnd->addMinutes($breakTime) > $timeChoose->second(59)) {
                $order['time_start'] = $timeStart->toDateTimeString();
                $order['time_end'] = $timeEnd->subMinutes($breakTime)->toDateTimeString();
                $listTime[] = $order;
            }
        }
        return $listTime;
    }

    public function getNumberOrder($date)
    {
        $max = $this->model->whereDate('date', $date->format('Y-m-d'))->max('number');
        return ($max ? $max : 0) + 1;
    }

    public function getTypeFee($method, $isFirst)
    {
        if ($method == Order::METHOD_FREE)
            return Order::TYPE_FREE;
        if ($isFirst) {
            if ($method == Order::METHOD_DIRECT_SHOP) {
                return Order::TYPE_PANEL;
            } elseif ($method == Order::METHOD_DIRECT) {
                return Order::TYPE_APPOINT;
            } else {
                return Order::TYPE_NET;
            }
        }
        return Order::TYPE_APPOINT;
    }

    public function getUnpaidOrderByPhone($phone)
    {
        $now = Carbon::now();
        if ($now->hour < CLOSE_HOURS - 24) {
            $now->subDay();
        }
        $tblOrder = $this->model->getTable();
        $tblCustomer = with(new Customer())->getTable();
        $tblCast = with(new Cast())->getTable();
        $tblCourse = with(new Course())->getTable();

        return $this->model::select(
            "$tblCustomer.name as customer_name",
            "$tblCustomer.customer_code as customer_code",
            "$tblCustomer.phone_number as customer_phone",
            "$tblCustomer.birthday as customer_birthday",
            "$tblCustomer.point as customer_point",
            'tblCast.name_cast as cast_name',
            'tblCourse.name as course_name',
            'tblCourse.time as course_time',
            "$tblOrder.*"
        )
            ->leftJoin("$tblCustomer", "$tblOrder.customer_id", '=', "$tblCustomer.id")
            ->leftJoin("$tblCast as tblCast", 'tblCast.id', '=', "$tblOrder.cast_id")
            ->leftJoin("$tblCourse as tblCourse", 'tblCourse.id', '=', "$tblOrder.course_id")
            ->where("$tblOrder.phone", $phone)
            ->whereNull("$tblOrder.deleted_at")
            ->where("$tblOrder.is_pay", Order::UNPAID)
            ->where("$tblOrder.status", '<>', Order::STATUS_CANCEL)
            ->whereDate("$tblOrder.date", '>=', $now->format('Y-m-d'))
            ->get();
    }

    public function getOrderSelectMember($orderId)
    {
        $tblOrder = $this->model->getTable();
        $tblCustomer = with(new Customer())->getTable();

        return $this->model->select(
            "$tblOrder.*",
            "$tblCustomer.customer_code as customer_code",
            "$tblCustomer.name as customer_name",
            "$tblCustomer.phone_number  as customer_phone",
            "$tblCustomer.point  as customer_point"
        )
            ->leftJoin("$tblCustomer", "$tblCustomer.id", '=', "$tblOrder.customer_id")
            ->where("$tblOrder.id", $orderId)
            ->first();
    }

    public function getOrderByCastId($castId, $date)
    {
        $tblCustomer = with(new Customer())->getTable();
        $tblCast = with(new Cast())->getTable();
        $tblOrder = $this->model->getTable();
        $orders = $this->model::leftjoin($tblCustomer, "$tblCustomer.id", '=', "$tblOrder.customer_id")
            ->join($tblCast, "$tblCast.id", '=', "$tblOrder.cast_id")
            ->select("$tblOrder.*", "$tblCustomer.name as customer_name", "$tblCast.name_cast")
            ->where('cast_id', '=', $castId)
            ->where("$tblOrder.status", "<>", Order::STATUS_CANCEL)
            ->whereDate("$tblOrder.date", $date)
            ->orderBy("$tblOrder.date_start")
            ->get();
        return $orders;
    }

    public function getOrderPayOfDay($date, $orderId = null)
    {
        $tblOrder = $this->model->getTable();
        $tblRoom = with(new Room())->getTable();
        $tblCastCalendar = with(new CastCalendar())->getTable();
        $tblOrderCoupon = with(new OrderCoupon())->getTable();
        $tblCoupon = with(new Coupon())->getTable();
        $tblCouponCourse = with(new CouponCourse())->getTable();
        $tblOrderOption = with(new OrderOption())->getTable();
        $tblOption = with(new Option())->getTable();
        $orders = $this->model->select(
            "$tblOrder.id",
            "$tblOrder.customer_id",
            "$tblOrder.number",
            "$tblOrder.cast_id",
            "$tblOrder.status",
            "$tblOrder.type_fee",
            "$tblOrder.method",
            "$tblOrder.is_first",
            "$tblOrder.date",
            "$tblOrder.date_start",
            "$tblOrder.date_end",
            "$tblOrder.actual_date_start",
            "$tblOrder.actual_date_end",
            "$tblOrder.course_id",
            "$tblOrder.name as name_customer",
            "$tblOrder.is_pay",
            "$tblOrder.minute_extension",
            "$tblOrder.course_price",
            "$tblOrder.price_fee_new",
            "$tblOrder.is_minute_add",
            "$tblOrder.is_ten_minutes_before",
            "$tblOrder.type_pay",
            "$tblOrder.price_cast",
            "$tblOrder.support_price",
            "$tblOrder.total_price",
            "$tblOrder.price_extension",
            "$tblOrder.minute_extension_setting",
            "$tblOrder.price_point",
            "$tblOrder.tax_percentage",
            "room_calendar_sub.room_id",
            "room_calendar_sub.name_room",
            //get coupon type 1
            "order_coupon_sub_1.coupon_id as coupon_1_id",
            "order_coupon_sub_1.coupon_name as coupon_1_name",
            "order_coupon_sub_1.coupon_price as coupon_1_price",
            //get coupon type 2
            "order_coupon_sub_2.coupon_id as coupon_2_id",
            "order_coupon_sub_2.coupon_name as coupon_2_name",
            "order_coupon_sub_2.coupon_price as coupon_2_price",
            //get coupon type 3
            "order_coupon_sub_3.coupon_id as coupon_3_id",
            "order_coupon_sub_3.coupon_name as coupon_3_name",
            "order_coupon_sub_3.coupon_price as coupon_3_price",
            //get option parent
            "order_option_parent.option_id as option_parent_id",
            "order_option_parent.option_name as option_parent_name",
            "order_option_parent.option_price as option_parent_price",
            //get option sub
            "order_option_sub.option_id as option_sub_id",
            "order_option_sub.option_name as option_sub_name",
            "order_option_sub.option_price as option_sub_price"
        )
            ->joinSub(DB::table($tblCastCalendar)
                ->leftjoin($tblRoom, "$tblRoom.id", '=', "$tblCastCalendar.room_id")
                ->select(
                    "$tblCastCalendar.cast_id",
                    "$tblCastCalendar.room_id",
                    "$tblRoom.name as name_room"
                )
                ->whereNull("$tblCastCalendar.deleted_at")
                ->whereDate("$tblCastCalendar.date", $date), 'room_calendar_sub', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.cast_id", '=', "room_calendar_sub.cast_id");
                })
            //get coupon type 1
            ->leftJoinSub(DB::table($tblOrderCoupon)
                ->join($tblCoupon, "$tblCoupon.id", '=', "$tblOrderCoupon.coupon_id")
                ->join($tblCouponCourse, "$tblCouponCourse.coupon_id", '=', "$tblCoupon.id")
                ->select(
                    "$tblCoupon.id as coupon_id",
                    "$tblCouponCourse.course_id as course_id",
                    "$tblOrderCoupon.order_id",
                    "$tblOrderCoupon.name as coupon_name",
                    "$tblOrderCoupon.price as coupon_price"
                )
                ->where("$tblCoupon.type", Coupon::TYPE_COUPON), 'order_coupon_sub_1', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.id", '=', "order_coupon_sub_1.order_id");
                    $join->on("$tblOrder.course_id", '=', "order_coupon_sub_1.course_id");
                })
            //get coupon type 2
            ->leftJoinSub(DB::table($tblOrderCoupon)
                ->join($tblCoupon, "$tblCoupon.id", '=', "$tblOrderCoupon.coupon_id")
                ->join($tblCouponCourse, "$tblCouponCourse.coupon_id", '=', "$tblCoupon.id")
                ->select(
                    "$tblCoupon.id as coupon_id",
                    "$tblCouponCourse.course_id as course_id",
                    "$tblOrderCoupon.order_id as order_id",
                    "$tblOrderCoupon.name as coupon_name",
                    "$tblOrderCoupon.price as coupon_price"
                )
                ->where("$tblCoupon.type", Coupon::TYPE_COUPON_MEMBER), 'order_coupon_sub_2', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.id", '=', "order_coupon_sub_2.order_id");
                    $join->on("$tblOrder.course_id", '=', "order_coupon_sub_2.course_id");
                })
            //get coupon type 3
            ->leftJoinSub(DB::table($tblOrderCoupon)
                ->join($tblCoupon, "$tblCoupon.id", '=', "$tblOrderCoupon.coupon_id")
                ->join($tblCouponCourse, "$tblCouponCourse.coupon_id", '=', "$tblCoupon.id")
                ->select(
                    "$tblCoupon.id as coupon_id",
                    "$tblCouponCourse.course_id as course_id",
                    "$tblOrderCoupon.order_id",
                    "$tblOrderCoupon.name as coupon_name",
                    "$tblOrderCoupon.price as coupon_price"
                )
                ->where("$tblCoupon.type", Coupon::TYPE_COUPON_TICKET), 'order_coupon_sub_3', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.id", '=', "order_coupon_sub_3.order_id");
                    $join->on("$tblOrder.course_id", '=', "order_coupon_sub_3.course_id");
                })
            //get option parent
            ->leftJoinSub(DB::table($tblOrderOption)
                ->join($tblOption, "$tblOption.id", '=', "$tblOrderOption.option_id")
                ->select(
                    "$tblOption.id as option_id",
                    "$tblOrderOption.order_id",
                    "$tblOrderOption.name as option_name",
                    "$tblOrderOption.price as option_price"
                )
                ->whereNull("$tblOption.parent"), 'order_option_parent', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.id", '=', "order_option_parent.order_id");
                })
            //get option sub
            ->leftJoinSub(DB::table($tblOrderOption)
                ->join($tblOption, "$tblOption.id", '=', "$tblOrderOption.option_id")
                ->select(
                    "$tblOption.id as option_id",
                    "$tblOrderOption.order_id",
                    "$tblOrderOption.name as option_name",
                    "$tblOrderOption.price as option_price"
                )
                ->whereNotNull("$tblOption.parent"), 'order_option_sub', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.id", '=', "order_option_sub.order_id");
                })
            ->whereDate("$tblOrder.date", $date)
            ->where("$tblOrder.status", "<>", Order::STATUS_CANCEL)
            ->where("$tblOrder.is_pay", '=', Order::PAID)
            ->groupBy("$tblOrder.id")
            ->orderBy("$tblOrder.status")
            ->orderBy("$tblOrder.date_start");
        if ($orderId) {
            $orders->where("$tblOrder.id", $orderId);
        }
        return $orders->get();
    }

    public function countOrderByStatus($status, $dateChoose = '')
    {
        $date = Carbon::now();
        if ($date->hour < CLOSE_HOURS - 24) {
            $date->subDay();
        }
        $date = $date->toDateString();
        if ($dateChoose) {
            $date = $dateChoose;
        }
        $orders = Order::whereDate('date', $date)
            ->where('status', $status)
            ->where('is_pay', '=', $this->model::PAID)
            ->count();
        return $orders;
    }

    public function getOrderOfCast($cast_id, $date, $isPay = false)
    {
        $orders = $this->model->select('orders.*')
            ->whereDate('date', $date)
            ->where('cast_id', '=', $cast_id)
            ->where('status', '!=', Order::STATUS_CANCEL);
        if ($isPay) {
            $orders = $orders->where('is_pay', '=', Order::PAID);
        }
        return $orders->orderBy('date_start')->get();
    }

    public function getTotalCustomerPresent($date, $isPay = false, $isToday = true)
    {
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        $collection = $this->model->whereIn('status', [Order::STATUS_HAPPENING, Order::STATUS_EXTENSION])
            ->whereDate('date', $dateTime->format('Y-m-d'))
            ->where('status', '<>', $this->model::STATUS_CANCEL);
        if ($isPay) {
            $collection->where('is_pay', Order::PAID);
        }
        return $collection->get()->count();
    }

    public function getTotalCustomerPresentByTime($dateStart, $dateEnd)
    {
        return $this->model->where('date_start', '<=', $dateEnd)
            ->whereDate('date', '>=', $dateStart)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->where('is_pay', Order::PAID)
            ->get()
            ->count();
    }

    public function getTotalPriceOrderPresent($date, $arrStatus, $isToday = true)
    {
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        return $this->model->selectRaw('sum(total_price) as total')
            ->whereDate('date', $dateTime->format('Y-m-d'))
            ->whereIn('status', $arrStatus)
            ->where('is_pay', Order::PAID)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->first();
    }

    public function getTotalRevenuePresentByTime($dateStart, $dateEnd)
    {
        return $this->model->where('is_pay', Order::PAID)
            ->where('date', '<=', $dateEnd)
            ->whereDate('date', '>=', $dateStart)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->sum('total_price');
    }

    public function getTotalCourseCateByDateStatus($date, $arrStatus = [], $isToday = true)
    {
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        $tblCourse = with(new Course())->getTable();

        return Course::select(
            "$tblCourse.*",
            "_orders.count_course",
            "_orders.total_course_revenue"
        )
            ->leftJoinSub(
                $this->model->selectRaw('course_id, count(course_id) as count_course, sum(total_price) as total_course_revenue')
                    ->whereDate('date', $dateTime->format('Y-m-d'))
                    ->whereIn('status', $arrStatus)
                    ->groupBy('course_id'),
                '_orders',
                function ($join) use ($tblCourse) {
                    $join->on('_orders.course_id', '=', "$tblCourse.id");
                }
            )
            ->orderBy('ordinal_number', 'ASC')
            ->get();
    }

    public function getTotalCourseCateByDateStatusPaid($date, $arrStatus = [], $isToday = true)
    {
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        $tblCourse = with(new Course())->getTable();

        return Course::select(
            "$tblCourse.*",
            "_orders.count_course",
            "_orders.total_course_revenue"
        )
            ->leftJoinSub(
                $this->model->selectRaw('course_id, count(course_id) as count_course, sum(total_price) as total_course_revenue')
                    ->whereDate('date', $dateTime->format('Y-m-d'))
                    ->whereIn('status', $arrStatus)
                    ->where('is_pay', Order::PAID)
                    ->groupBy('course_id'),
                '_orders',
                function ($join) use ($tblCourse) {
                    $join->on('_orders.course_id', '=', "$tblCourse.id");
                }
            )
            ->orderBy('ordinal_number', 'ASC')
            ->get();
    }

    public function getOrderPaysByDate($date, $isToday = true)
    {
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        return $this->model->whereDate('date', $dateTime->format('Y-m-d'))
            ->where('is_pay', '=', $this->model::PAID)
            ->orderBy('status')
            ->orderBy('date_start')
            ->get();
    }

    public function getTotalOrderTypeFee($date, $isToday = true)
    {
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        return $this->model->select(
            'type_fee',
            DB::raw('count(type_fee) as count_type_fee')
        )
            ->whereDate('date', $dateTime->format('Y-m-d'))
            ->where('status', "<>", Order::STATUS_CANCEL)
            ->where('is_pay', '=', Order::PAID)
            ->groupBy('type_fee')
            ->get();
    }

    public function getTotalMethodGroupFee($date, $isToday = true)
    {
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        return $this->model->select(
            'method',
            'type_fee'
        )
            ->whereDate('date', $dateTime->format('Y-m-d'))
            ->where('status', "<>", Order::STATUS_CANCEL)
            ->where('is_pay', '=', Order::PAID)
            ->get();
    }

    public function getLabelTypeFees()
    {
        return $this->model->getLabelTypeFees();
    }

    public function getTotalOrderExtension($date, $isToday = true)
    {
        $dateChoose = clone $date;
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        return $this->model->select(
            DB::raw('count(id) as count_extension'),
            DB::raw('sum(minute_extension) as total_minute_extension')
        )
            ->where('date_start', '<=', $dateChoose)
            ->whereDate('date', $dateTime->format('Y-m-d'))
            ->WhereNotNull('minute_extension')
            ->where('minute_extension', '<>', 0)
            ->first();
    }

    public function getTotalTypeFeeOfCast($date, $isToday = true)
    {
        $dateTime = clone $date;
        if (!$isToday) {
            $dateTime->subDay();
        }
        return $this->model->select(
            'cast_id',
            'type_fee',
            DB::raw('count(type_fee) as count_type_fee')
        )
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->whereDate('date', $dateTime->format('Y-m-d'))
            ->groupBy('cast_id')
            ->groupBy('type_fee')
            ->get();
    }

    public function getRevenueOrderGroupDate($dateStart, $dateEnd, $groupBy = 'date')
    {
        $tblOrderCoupon = 'order_coupon';
        $tblOrderOption = 'order_option';
        $tblOrder = 'orders';
        $tblSetting = 'settings';
        $percentService = OrderService::PERCENT / 100;
        $typePayment = Orders::TYPE_PAYMENT_CARD;
        $extensionPrice = DB::raw("{$tblSetting}.price * COALESCE(NULLIF({$tblOrder}.minute_extension, 0), 0) / {$tblSetting}.minute_extension");

        $collection = $this->model->select(
            DB::raw("SUM(
                CASE
                    WHEN $tblOrder.type_pay = $typePayment THEN
                        (total_price - $extensionPrice) / (1 + $percentService) + $extensionPrice
                    ELSE total_price 
                END
            ) as total_price_cash_orders"),
            DB::raw("SUM($tblOrder.total_price) as total_price_orders"),
            DB::raw("SUM($tblOrder.price_cast) as total_price_cast"),
            DB::raw("SUM(_orders.coupon_price) as total_price_coupon"),
            DB::raw("SUM(CASE WHEN _orders.option_price = 1 THEN 1 ELSE 0 END) as total_option"),
            DB::raw("SUM(CASE WHEN {$tblOrder}.minute_extension IS NOT NULL OR {$tblOrder}.minute_extension <> 0 THEN 1 ELSE 0 END) as total_extension"),
            DB::raw("COUNT($tblOrder.phone) as total_customers")
        )
            ->crossJoin("$tblSetting")
            ->leftJoinSub(
                $this->model->select(
                    'orders.id',
                    DB::raw("SUM($tblOrderCoupon.price) as coupon_price"),
                    DB::raw("COUNT($tblOrderOption.price) as option_price")
                )
                    ->leftJoin("$tblOrderCoupon", "$tblOrderCoupon.order_id", '=', "$tblOrder.id")
                    ->leftJoin("$tblOrderOption", "$tblOrderOption.order_id", '=', "$tblOrder.id")
                    ->whereDate('date', '>=', $dateStart)
                    ->whereDate('date', '<=', $dateEnd)
                    ->where('status', '<>', Order::STATUS_CANCEL)
                    ->where('is_pay', Order::PAID)
                    ->groupBy('orders.id'),
                '_orders',
                function ($join) {
                    $join->on('_orders.id', '=', 'orders.id');
                }
            )
            ->whereDate('date', '>=', $dateStart)
            ->whereDate('date', '<=', $dateEnd)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->where('is_pay', Order::PAID);

        if ($groupBy == 'date') {
            $collection->addSelect('date')
                ->groupBy('date')
                ->orderBy('date');
        } else {
            $collection->addSelect(DB::raw("DATE_FORMAT(date, '%Y-%m') as month"))
                ->groupBy('month')
                ->orderBy('month');
        }

        return $collection->get();
    }

    public function getCourseOrderGroupDate($dateStart, $dateEnd, $groupBy = 'date')
    {
        $collection = $this->model->select(
            'course_id',
            DB::raw('count(course_id) as count_course')
        )
            ->whereDate('date', '>=', $dateStart)
            ->whereDate('date', '<=', $dateEnd)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->where('is_pay', Order::PAID);

        if ($groupBy == 'date') {
            $collection->addSelect('date')
                ->groupBy('date', 'course_id')
                ->orderBy('date');
        } else {
            $collection->addSelect(DB::raw("DATE_FORMAT(date, '%Y-%m') as month"))
                ->groupBy(DB::raw("year(date), month(date), course_id"))
                ->orderBy(DB::raw("DATE_FORMAT(date, '%Y-%m')"));
        }

        return $collection->get();
    }

    public function setTimeOrder($status, $timeStart, $timeEnd)
    {
        switch ($status) {
            case Order::STATUS_NO_HAPPEN_YET:
                return [
                    'date_start' => $timeStart,
                    'date_end' => $timeEnd
                ];
                break;
            case Order::STATUS_HAPPENING:
            case Order::STATUS_EXTENSION:
                return [
                    'actual_date_start' => $timeStart,
                    'date_end' => $timeEnd
                ];
                break;
            default:
                return [
                    'actual_date_start' => $timeStart,
                    'actual_date_end' => $timeEnd
                ];
                break;
        }
    }

    public function calculatePriceCourse($order, $courseNew, $tax)
    {
        $priceCourseOld = $order->course_price;
        $priceCourseNew = $courseNew->price;
        if ($courseNew->tax_included == Course::NO_TAX_INCLUDED) {
            $priceCourseNew *= (1 + $tax / 100);
        }
        $priceCourseChange = $priceCourseNew - $priceCourseOld;
        return $priceCourseChange;
    }

    public function getOrdersByPhone($phone)
    {
        return $this->model->where('phone', $phone)->get();
    }

    public function checkIsTimePlus($castId, $dateChoose, $dateChooseEnd, $breakTime, $orderId = null)
    {
        $listTime = $this->model->select('orders.*')
            ->crossJoin('settings')
            ->join('casts', 'casts.id', '=', 'orders.cast_id')
            ->where('casts.id', $castId)
            ->whereRaw("orders.date_start < DATE_ADD(?, INTERVAL ? + settings.minute_add MINUTE) and DATE_ADD(orders.date_end, INTERVAL ? + settings.minute_add + if(orders.minute_extension is null, 0, orders.minute_extension)  MINUTE) > ?", [
                $dateChooseEnd->toDateTimeString(),
                $breakTime,
                $breakTime,
                $dateChoose->second(59)->toDateTimeString()
            ])->where("orders.status", '<>', Order::STATUS_CANCEL);
        if ($orderId) {
            $listTime = $listTime->where('orders.id', '<>', $orderId);
        }
        $listTime = $listTime->orderBy('orders.date_start')->get();
        $dateChoose->second(0);
        return count($listTime) == 0;
    }

    public function getOrderByID($orderID)
    {
        return $this->model->where('id', $orderID)->first();
    }

    public function getOrderInDay($date)
    {
        return $this->model->whereDate('date', $date->format('Y-m-d'))
            ->where("orders.status", '<>', Order::STATUS_CANCEL)
            ->orderBy('date_start')
            ->get();
    }

    public function updateOrderByCustomerId($customerId, $phone, $name)
    {
        return $this->model->where('customer_id', $customerId)
            ->update([
                'phone' => $phone,
                'phone_point' => $phone,
                'name' => $name
            ]);
    }

    public function getListOrderByCastAndDate($castId, $date, $arrMethod = [])
    {
        $listOrder = $this->model->select(
            'orders.*',
            DB::raw('CASE WHEN orders.actual_date_start is null THEN orders.date_start ELSE orders.actual_date_start
                    END AS time_start'),
            DB::raw('CASE WHEN orders.actual_date_end is null THEN orders.date_end ELSE orders.actual_date_end
                    END AS time_end')
        )
            ->where('cast_id', $castId)
            ->whereDate('date', $date->format('Y-m-d'))
            ->where("orders.status", '<>', Order::STATUS_CANCEL);
        if ($arrMethod) {
            $listOrder = $listOrder->whereIn('method', $arrMethod);
        }
        $listOrder = $listOrder->orderBy('orders.date_start')->get();
        return $listOrder;
    }

    public function updatePriceSupport($itemId, $price, $priceChange)
    {
        $now = Carbon::now();
        if ($now->hour < CLOSE_HOURS - 24) {
            $now->subDay();
        }
        $tbOrder = with(new Order())->getTable();
        $tbCastCalendar = with(new CastCalendar())->getTable();
        DB::update("UPDATE $tbOrder
            SET support_price = $price, price_cast = price_cast + $priceChange
            WHERE date = ? and support_id = $itemId", [$now->toDateString()]);
        $orders = $this->model->select('cast_id')->where('date', $now->toDateString())->where('support_id', $itemId)->get();
        foreach ($orders as $order) {
            DB::update("UPDATE $tbCastCalendar
            SET price_cast = price_cast + $priceChange
            WHERE date = ? and cast_id = $order->cast_id", [$now->toDateString()]);
        }
        return true;
    }

    public function totalPriceCastOrderPresentByTime($dateStart, $dateEnd)
    {
        return $this->model->selectRaw('sum(support_price) as total_support_price')
            ->selectRaw('sum(price_cast) as total_price_cast')
            ->where('date', '>=', $dateStart)
            ->where('date', '<=', $dateEnd)
            ->where('is_pay', Order::PAID)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->first();
    }

    public function totalOrdersByCardByTime($dateStart, $dateEnd)
    {
        $tblCastCalendar = "cast_calendars";
        $tblOrder = "orders";
        $fee = OrderService::PERCENT;

        $totalPriceOrder = $this->model->selectRaw('sum(total_price) as total_price')
            ->selectRaw("sum(total_price / (1 + $fee / 100)) as total_tax_price")
            ->selectRaw('count(*) as total')
            ->where('date', '>=', $dateStart)
            ->where('date', '<=', $dateEnd)
            ->where('is_pay', Order::PAID)
            ->where('type_pay', Orders::TYPE_PAYMENT_CARD)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->first();

        $totalPriceCast = $this->model->selectRaw('sum(price_cast) as total_price_cast')
            ->selectRaw("cast_calendar_sub.is_pay_calendar as is_pay_calendar")
            ->joinSub(DB::table($tblCastCalendar)
                ->select(
                    "$tblCastCalendar.is_pay as is_pay_calendar",
                    "$tblCastCalendar.cast_id",
                    "$tblCastCalendar.date"
                )
                ->whereNull("$tblCastCalendar.deleted_at"), 'cast_calendar_sub', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.cast_id", '=', "cast_calendar_sub.cast_id");
                    $join->on("$tblOrder.date", '=', "cast_calendar_sub.date");
                })
            ->where('is_pay_calendar', CastCalendar::PAID)
            ->where("$tblOrder.date", '>=', $dateStart)
            ->where("$tblOrder.date", '<=', $dateEnd)
            ->where("$tblOrder.is_pay", Order::PAID)
            ->where("$tblOrder.type_pay", Orders::TYPE_PAYMENT_CARD)
            ->where("$tblOrder.status", '<>', Order::STATUS_CANCEL)
            ->first();

        return [
            'total_price' => $totalPriceOrder->total_price,
            'total_tax_price' => $totalPriceOrder->total_tax_price,
            'total' => $totalPriceOrder->total,
            'total_price_cast' => $totalPriceCast->total_price_cast,
        ];
    }

    public function totalOrdersByCashByTime($dateStart, $dateEnd)
    {
        $tblCastCalendar = "cast_calendars";
        $tblOrder = "orders";
        $totalPriceOrder = $this->model->selectRaw('sum(total_price) as total_price')
            ->where('date', '>=', $dateStart)
            ->where('date', '<=', $dateEnd)
            ->where('is_pay', Order::PAID)
            ->where('type_pay', Orders::TYPE_PAYMENT_CASH)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->first()->total_price;
        $totalPriceCast = $this->model->selectRaw('sum(price_cast - support_price) as total_price_cast_not_support')
            ->selectRaw('sum(support_price) as total_price_support')
            ->selectRaw("cast_calendar_sub.is_pay_calendar as is_pay_calendar")
            ->joinSub(DB::table($tblCastCalendar)
                ->select(
                    "$tblCastCalendar.is_pay as is_pay_calendar",
                    "$tblCastCalendar.cast_id",
                    "$tblCastCalendar.date"
                )
                ->whereNull("$tblCastCalendar.deleted_at"), 'cast_calendar_sub', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.cast_id", '=', "cast_calendar_sub.cast_id");
                    $join->on("$tblOrder.date", '=', "cast_calendar_sub.date");
                })
            ->where('is_pay_calendar', CastCalendar::PAID)
            ->where("$tblOrder.date", '>=', $dateStart)
            ->where("$tblOrder.date", '<=', $dateEnd)
            ->where("$tblOrder.is_pay", Order::PAID)
            ->where("$tblOrder.type_pay", Orders::TYPE_PAYMENT_CASH)
            ->where("$tblOrder.status", '<>', Order::STATUS_CANCEL)
            ->first();

        return [
            'total_price' => $totalPriceOrder,
            'total_price_cast_not_support' => $totalPriceCast->total_price_cast_not_support,
            'total_price_support' => $totalPriceCast->total_price_support
        ];
    }

    public function totalOrdersByCardInDay15ByTime($dateStart, $dateEnd)
    {
        $tblCastCalendar = "cast_calendars";
        $tblOrder = "orders";
        $fee = OrderService::PERCENT;
        $totalOrders = $this->model->selectRaw('count(*) as total')
            ->selectRaw("sum(total_price / (1 + $fee / 100)) as total_tax_price")
            ->selectRaw('sum(total_price) as total_price')
            ->whereRaw('day(date) < ?', [Orders::DAY_CARD_ORDER])
            ->whereRaw('month(date) >= ?', [$dateStart->month])
            ->whereRaw('month(date) <= ?', [$dateEnd->month])
            ->whereRaw('year(date) = ?', [$dateStart->year])
            ->where('is_pay', Order::PAID)
            ->where('type_pay', Orders::TYPE_PAYMENT_CARD)
            ->where('status', '<>', Order::STATUS_CANCEL)
            ->first();

        $totalPriceCast = $this->model->selectRaw('sum(price_cast) as total_price_cast')
            ->joinSub(DB::table($tblCastCalendar)
                ->select(
                    "$tblCastCalendar.is_pay as is_pay_calendar",
                    "$tblCastCalendar.cast_id",
                    "$tblCastCalendar.date"
                )
                ->whereNull("$tblCastCalendar.deleted_at"), 'cast_calendar_sub', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.cast_id", '=', "cast_calendar_sub.cast_id");
                    $join->on("$tblOrder.date", '=', "cast_calendar_sub.date");
                })
            ->where('is_pay_calendar', CastCalendar::PAID)
            ->whereRaw("day($tblOrder.date) < ?", [Orders::DAY_CARD_ORDER])
            ->whereRaw("month($tblOrder.date) >= ?", [$dateStart->month])
            ->whereRaw("month($tblOrder.date) <= ?", [$dateEnd->month])
            ->whereRaw("year($tblOrder.date) = ?", [$dateStart->year])
            ->where("$tblOrder.is_pay", Order::PAID)
            ->where("$tblOrder.type_pay", Orders::TYPE_PAYMENT_CARD)
            ->where("$tblOrder.status", '<>', Order::STATUS_CANCEL)
            ->first();

        return [
            'total' => $totalOrders->total,
            'total_price' => $totalOrders->total_price,
            'total_tax_price' => $totalOrders->total_tax_price,
            'total_price_cast' => $totalPriceCast->total_price_cast,
        ];
    }

    public function listOrdersByCardInMonth($date)
    {
        $tbOrder = with(new Order())->getTable();
        $tbCast = with(new Cast())->getTable();
        $tbCourse = with(new Course())->getTable();
        $tbSettingPriceSupport = with(new SettingPriceSupport())->getTable();

        $listOrder = $this->model->select("$tbOrder.*", "$tbCourse.name as name_course", "$tbCast.name_cast as name_cast", "$tbSettingPriceSupport.name as name_support")
            ->leftjoin($tbCourse, "$tbOrder.course_id", '=', "$tbCourse.id")
            ->leftjoin($tbCast, "$tbOrder.cast_id", '=', "$tbCast.id")
            ->leftjoin($tbSettingPriceSupport, "$tbOrder.support_id", '=', "$tbSettingPriceSupport.id")
            ->whereRaw("month($tbOrder.date) = ?", [$date->month])
            ->whereRaw("year($tbOrder.date) = ?", [$date->year])
            ->where("$tbOrder.is_pay", Order::PAID)
            ->where("$tbOrder.type_pay", Orders::TYPE_PAYMENT_CARD)
            ->where("$tbOrder.status", '<>', Order::STATUS_CANCEL)
            ->get();

        return $listOrder;
    }

    public function totalPriceForCastOrderByTime($dateStart, $dateEnd)
    {
        $tblCastCalendar = "cast_calendars";
        $tblOrder = "orders";

        return $this->model->selectRaw('sum(price_cast) as total_price_cast')
            ->joinSub(DB::table($tblCastCalendar)
                ->select(
                    "$tblCastCalendar.is_pay as is_pay_calendar",
                    "$tblCastCalendar.cast_id",
                    "$tblCastCalendar.date"
                )
                ->whereNull("$tblCastCalendar.deleted_at"), 'cast_calendar_sub', function ($join) use ($tblOrder) {
                    $join->on("$tblOrder.cast_id", '=', "cast_calendar_sub.cast_id");
                    $join->on("$tblOrder.date", '=', "cast_calendar_sub.date");
                })
            ->where('is_pay_calendar', CastCalendar::PAID)
            ->where("$tblOrder.date", '>=', $dateStart)
            ->where("$tblOrder.date", '<=', $dateEnd)
            ->where("$tblOrder.is_pay", Order::PAID)
            ->where("$tblOrder.status", '<>', Order::STATUS_CANCEL)
            ->first()->total_price_cast;
    }

    public function updateMethodByTypeFee($typeFee)
    {
        if ($typeFee == $this->model::TYPE_NET) {
            return $this->model::METHOD_NET_HEAVEN;
        }
        if ($typeFee == $this->model::TYPE_PANEL) {
            return $this->model::METHOD_PHONE;
        }
        if ($typeFee == $this->model::TYPE_FREE) {
            return $this->model::METHOD_FREE;
        }

        return $this->model::METHOD_DIRECT_SHOP;
    }

    public function updateIsFirstByTypeFee($typeFee)
    {
        if ($typeFee != $this->model::TYPE_APPOINT) {
            return $this->model::FIRST_TIME;
        }

        return $this->model::MANY_TIME;
    }

    public function getAllOrderOfMemberByCastId($castId)
    {
        $tblCustomer = with(new Customer())->getTable();
        $tblCast = with(new Cast())->getTable();
        $tblCourse = with(new Course())->getTable();
        $tblOrderCoupon = with(new OrderCoupon())->getTable();
        $tblOrder = $this->model->getTable();
        $orders = $this->model::join($tblCustomer, "$tblCustomer.id", '=', "$tblOrder.customer_id")
            ->join($tblCast, "$tblCast.id", '=', "$tblOrder.cast_id")
            ->join($tblCourse, "$tblCourse.id", '=', "$tblOrder.course_id")
            ->leftJoin($tblOrderCoupon, "$tblOrderCoupon.order_id", '=', "$tblOrder.id")
            ->select(
                "$tblOrder.*",
                "$tblCustomer.name as customer_name",
                "$tblCustomer.phone_number as customer_phone_number",
                "$tblCustomer.reason_ng_customer",
                "$tblCourse.name as course_name",
                DB::raw("sum($tblOrderCoupon.price) as coupon_price")
            )
            ->where('cast_id', '=', $castId)
            ->where("$tblOrder.is_pay", Order::PAID)
            ->orderByDesc("$tblOrder.date_start")
            ->groupBy("$tblOrder.id")
            ->paginate(100);
        return $orders;
    }

    public function getTotalPriceExpensesOrdersPaid($dateStart, $dateEnd, $isToday = true)
    {
        $dateTimeStart = clone $dateStart;
        $dateTimeEnd = clone $dateEnd;
        if ($dateStart == $dateEnd && !$isToday) {
            $dateTimeStart->subDay();
            $dateTimeEnd->subDay();
        }

        return $this->model::selectRaw('sum(orders.price_cast * ifnull(cast_calendars.expenses, 0) / 100) as total_price_expenses')
            ->join('cast_calendars', function ($join) {
                $join->on('cast_calendars.date', '=', 'orders.date')
                    ->on('cast_calendars.cast_id', '=', 'orders.cast_id');
            })
            ->where("orders.date", '>=', $dateStart->format('Y-m-d'))
            ->where("orders.date", '<=', $dateEnd->format('Y-m-d'))
            ->where("orders.status", '<>', Order::STATUS_CANCEL)
            ->where("orders.is_pay", Order::PAID)
            ->whereNull("orders.deleted_at")
            ->whereNull("cast_calendars.deleted_at")
            ->get();
    }
}
