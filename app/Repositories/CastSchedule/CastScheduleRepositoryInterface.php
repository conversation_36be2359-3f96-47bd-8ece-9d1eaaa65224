<?php

namespace  App\Repositories\CastSchedule;

use App\Repositories\RepositoryInterface;

interface CastScheduleRepositoryInterface
{
    /**
     * get number table 
     *
     * @param  Carbon $dateSrart
     * @param Carbon $dateEnd
     * @return int
     */
    public function getNummberTable($dateStart, $dateEnd);

    /**
     * set attribute frame time order
     *
     * @param  ModelOrder $orders 
     * @return array
     */
    public function settingOrderTime($orders = []);

    /**
     * set time rest after a order
     *
     * @param  ModelOrder $orders 
     * @param int $interval
     * @return array
     */
    public function setTimeRest($interval, $orders = []);

    /**
     * round time to even
     * 
     * @param  carbon $date
     * @return Carbon
     */
    public function roundTimeOrder($date);
}
