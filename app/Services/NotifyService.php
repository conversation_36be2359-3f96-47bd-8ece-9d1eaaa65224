<?php

namespace App\Services;

use Throwable;
use Illuminate\Support\Facades\Log;
use App\Jobs\NotifyJob;

class NotifyService
{
    public static function msTeam(Throwable $exception, $request)
    {
        try {
            $msTeamNotify = env("MS_TEAM_NOTIFY", false);
            if ($msTeamNotify) {
                $message = "<b style='color:red'>WARNING</b>";
                $message .= "<br/>Url: <b>" . url()->full() . "</b>";
                try {

                    if (auth()->id()) {
                        $message .= "<br/>UserID: " . auth()->id();
                        $message .= "<br/>Username: " . auth()->user()->email;
                    }
                } catch (Throwable $th2) {
                    Log::info($th2);
                }
                if ($request->ip()) {
                    $message .= "<br/>IP Address: " . $request->ip();
                }
                if ($request->header('user-agent')) {
                    $message .= "<br/>User Agent: " . $request->header('user-agent');
                }
                if (method_exists($exception, "getMessage")) {
                    $message .= "<br/>Message: " . $exception->getMessage();
                }
                if ($request->all()) {
                    $message .= "<br/>Data: " . json_encode($request->all());
                }
                if (method_exists($exception, "getTraceAsString")) {
                    $message .= "<br/><hr/>Trace: " . $exception->getTraceAsString();
                }

                NotifyJob::dispatch($message);
            }
        } catch (Throwable $th) {
            Log::info("ERROR: CUSTOM HANDLER");
            Log::info($th);
        }
    }
}
