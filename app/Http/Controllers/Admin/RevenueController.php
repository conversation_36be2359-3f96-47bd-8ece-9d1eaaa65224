<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CastCalendar;
use App\Models\Order;
use App\Repositories\CastCalendar\CastCalendarRepository;
use App\Repositories\CastPriceLiving\CastPriceLivingRepository;
use App\Repositories\CostShop\CostShopRepository;
use App\Repositories\Course\CourseRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\PriceDecorateRoom\PriceDecorateRoomRepository;
use App\Repositories\Room\RoomCalendarRepository;
use App\Repositories\Setting\SettingRepository;
use App\Repositories\DailyRevenue\DailyRevenueRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RevenueController extends Controller
{
    private $castCalendarRepository;
    private $courseRepository;
    private $orderRepository;
    private $roomCalendarRepository;
    private $costShopRepository;
    private $castPriceLivingRepository;
    private $priceDecorateRoomRepository;
    private $settingRepository;
    private $dailyRevenueRepository;

    public function __construct(
        CastCalendarRepository $castCalendarRepository,
        CourseRepository $courseRepository,
        OrderRepository $orderRepository,
        RoomCalendarRepository $roomCalendarRepository,
        CostShopRepository $costShopRepository,
        CastPriceLivingRepository $castPriceLivingRepository,
        PriceDecorateRoomRepository $priceDecorateRoomRepository,
        SettingRepository $settingRepository,
        DailyRevenueRepository $dailyRevenueRepository
    ) {
        $this->courseRepository = $courseRepository;
        $this->orderRepository = $orderRepository;
        $this->castCalendarRepository = $castCalendarRepository;
        $this->roomCalendarRepository = $roomCalendarRepository;
        $this->costShopRepository = $costShopRepository;
        $this->castPriceLivingRepository = $castPriceLivingRepository;
        $this->priceDecorateRoomRepository = $priceDecorateRoomRepository;
        $this->settingRepository = $settingRepository;
        $this->dailyRevenueRepository = $dailyRevenueRepository;
    }

    public function index(Request $request)
    {
        $date = Carbon::now();
        $dateShow = clone $date;
        $isToday = true;
        if (isset($request->date) && $request->date) {
            $date = Carbon::parse($request->date . ' ' . $date->format('H:i'));
            $dateShow = clone $date;
        } else {
            if ($dateShow->hour < CLOSE_HOURS - 24) {
                $dateShow->subDay();
                $isToday = false;
            }
        }
        if ($date < dateCanView()) {
            return redirect()->route('access.deny');
        }
        $arrStatusRevenue = [
            Order::STATUS_HAPPENING,
            Order::STATUS_EXTENSION,
            Order::STATUS_FINISH
        ];

        $arrStatus = [
            Order::STATUS_NO_HAPPEN_YET,
            Order::STATUS_HAPPENING,
            Order::STATUS_EXTENSION,
            Order::STATUS_FINISH
        ];

        $filtersWork['arr_status'] = [
            CastCalendar::STATUS_PREPARE,
            CastCalendar::STATUS_WORKING
        ];

        $filtersWorking['arr_status'] = [
            CastCalendar::STATUS_WORKING
        ];

        $filtersCast['arr_status'] = [
            CastCalendar::STATUS_WORKING,
            CastCalendar::STATUS_FINISH,
        ];

        $this->data['totalTypeFeeCast'] = [];
        $this->data['totalMethodGroupFee'] = [];
        $this->data['date'] = $dateShow;
        $this->data['totalCustomer'] = $this->orderRepository->getTotalCustomerPresent($date, true, $isToday);
        $this->data['totalPriceCast'] = $this->castCalendarRepository->getTotalPriceCastPresentByTime($date, $date, $isToday)->total;
        $this->data['totalCourseCate'] = $this->orderRepository->getTotalCourseCateByDateStatus($date, $arrStatus, $isToday);
        $setting = $this->settingRepository->getSetting();
        $taxPercentage = $setting->tax_percentage;
        $this->data['minuteExtension'] = $setting->minute_extension;
        $this->data['priceExtension'] = $setting->price * (1 + $taxPercentage / 100);
        $this->data['priceCastExtension'] = $setting->price_cast;
        $this->data['priceMinuteAdd'] = $setting->price_minute_add;
        $this->data['dataOrders'] = $this->orderRepository->getOrderPayOfDay($dateShow->toDateString());
        $this->data['totalCourseCateWorking'] = $this->orderRepository->getTotalCourseCateByDateStatusPaid($date, $arrStatus, $isToday);
        $this->data['totalOrderByTypeFee'] = $this->orderRepository->getTotalOrderTypeFee($date, $isToday)->keyBy('type_fee')->toArray();
        $totalMethodGroupFee = $this->orderRepository->getTotalMethodGroupFee($date, $isToday);
        $this->data['arrLabelTypeFee'] = $this->orderRepository->getLabelTypeFees();
        $this->data['totalOrderExtension'] = $this->orderRepository->getTotalOrderExtension($date, $isToday);
        $this->data['castCalendars'] = $this->castCalendarRepository->getCastCalendarSortStatus($date, $filtersWork, $isToday);
        $this->data['castRoomCalendars'] = $this->roomCalendarRepository->getListNameRoomCastByDate($date, $isToday)->keyBy('cast_id')->toArray();
        $this->data['totalCast'] = $this->castCalendarRepository->getCastCalendarSortStatus($date, $filtersCast, $isToday)->count();
        $this->data['totalCastWorking'] = $this->castCalendarRepository->getCastCalendarSortStatus($date, $filtersWorking, $isToday)->count();
        $this->data['listCostShop'] = $this->costShopRepository->getAllByTime($dateShow->toDateString(), $dateShow->toDateString());
        $this->data['totalStatistic'] = $this->getTotalStatisticByTime($dateShow, $dateShow);

        $dataTotalTypeFee = $this->orderRepository->getTotalTypeFeeOfCast($date, $isToday);
        if ($dataTotalTypeFee->count()) {
            foreach ($dataTotalTypeFee as $item) {
                $this->data['totalTypeFeeCast'][$item->cast_id][$item->type_fee] = $item;
            }
        }
        if ($totalMethodGroupFee->count()) {
            foreach ($totalMethodGroupFee as $item) {
                if ($item->type_fee == Order::TYPE_NET) {
                    if (isset($this->data['totalMethodNetFee'][$item->method])) {
                        $this->data['totalMethodNetFee'][$item->method]++;
                    } else {
                        $this->data['totalMethodNetFee'][$item->method] = 1;
                    }
                }
            }
        }
        $this->data['arrCountBookByStatus'] = [];
        foreach ($arrStatus as $status) {
            $count = $this->orderRepository->countOrderByStatus($status, $dateShow);
            array_push($this->data['arrCountBookByStatus'], $count);
        }
        return $this->renderView('admin.revenue.list');
    }

    public function getTotalStatisticByTime($dateStart, $dateEnd)
    {
        // Use optimized version if data exists in daily_revenues table
        try {
            return $this->dailyRevenueRepository->getTotalStatisticByTimeOptimized($dateStart, $dateEnd);
        } catch (Exception $e) {
            // Fallback to legacy method if optimized version fails
            dd(123);
            return $this->getTotalStatisticByTimeLegacy($dateStart, $dateEnd);
        }
    }

    public function getTotalStatisticByTimeLegacy($dateStart, $dateEnd)
    {
        $totalPriceCastPaied = $this->castCalendarRepository->getTotalPriceCastPaiedByTime($dateStart, $dateEnd);
        $total_price_expenses = $this->orderRepository->getTotalPriceExpensesOrdersPaid($dateStart, $dateEnd);
        $totalStatistic['priceForCast'] = $totalPriceCastPaied->price_for_cast;
        $totalStatistic['dormitoryPrice'] = $totalPriceCastPaied->total_dormitory_price;
        $totalStatistic['expenses'] = isset($total_price_expenses[0]->total_price_expenses) ? $total_price_expenses[0]->total_price_expenses : 0;
        $totalStatistic['priceLiving'] = $this->castPriceLivingRepository->totalPriceByTime($dateStart->toDateString(), $dateEnd->toDateString());
        $totalStatistic['priceDecorateRoom'] = $this->priceDecorateRoomRepository->getTotalPriceByTime($dateStart, $dateEnd);
        $totalPriceOrderByCard = $this->orderRepository->totalOrdersByCardByTime($dateStart->toDateString(), $dateEnd->toDateString());
        $totalStatistic['numberOrdersByCard'] = $totalPriceOrderByCard['total'];
        $totalStatistic['priceOrdersByCard'] = $totalPriceOrderByCard['total_price'];
        $totalStatistic['taxPriceOrdersByCard'] = $totalPriceOrderByCard['total_tax_price'];
        $totalStatistic['priceCastOrdersByCard'] = $totalPriceOrderByCard['total_price_cast'];
        $totalPriceOrderByCardInDay15 = $this->orderRepository->totalOrdersByCardInDay15ByTime($dateStart, $dateEnd);
        $totalStatistic['numberOrdersByCardInDay15'] = $totalPriceOrderByCardInDay15['total'];
        $totalStatistic['priceCastOrdersByCardInDay15'] = $totalPriceOrderByCardInDay15['total_price_cast'];
        $totalStatistic['taxPriceOrdersByCardInDay15'] = $totalPriceOrderByCardInDay15['total_tax_price'];
        $totalStatistic['totalPriceOrdersByCardInDay15'] = $totalPriceOrderByCardInDay15['total_price'];
        $totalStatistic['numberOrdersByCardInLastMonth'] = $totalPriceOrderByCard['total'] - $totalPriceOrderByCardInDay15['total'];
        $totalStatistic['priceCastOrdersByCardInLastMonth'] = $totalPriceOrderByCard['total_price_cast'] - $totalPriceOrderByCardInDay15['total_price_cast'];
        $totalStatistic['taxPriceCastOrdersByCardInLastMonth'] = $totalPriceOrderByCard['total_tax_price'] - $totalPriceOrderByCardInDay15['total_tax_price'];
        $totalStatistic['totalPriceOrdersByCardInLastMonth'] = $totalPriceOrderByCard['total_price'] - $totalPriceOrderByCardInDay15['total_price'];
        $totalPriceOrderByCash = $this->orderRepository->totalOrdersByCashByTime($dateStart->toDateString(), $dateEnd->toDateString());
        $totalStatistic['priceOrdersByCash'] = $totalPriceOrderByCash['total_price'];
        $totalStatistic['priceCastNotSupportByCash'] = $totalPriceOrderByCash['total_price_cast_not_support'];
        $totalStatistic['priceSupportByCash'] = $totalPriceOrderByCash['total_price_support'];
        $totalStatistic['costShop'] = $this->costShopRepository->totalPriceByTime($dateStart->toDateString(), $dateEnd->toDateString());
        $totalStatistic['totalRevenue'] = $totalStatistic['dormitoryPrice'] + $totalStatistic['expenses'] + $totalStatistic['priceLiving'] + $totalStatistic['priceDecorateRoom'] - $totalStatistic['priceCastNotSupportByCash'] - $totalStatistic['priceSupportByCash'] - $totalStatistic['costShop'];
        $totalStatistic['priceOrderForCast'] = $this->orderRepository->totalPriceForCastOrderByTime($dateStart->toDateString(), $dateEnd->toDateString());
        return $totalStatistic;
    }

    public function getStatisticForMonth(Request $request)
    {
        $groupBy = 'date';
        $dateShow = Carbon::now();
        if ($dateShow->hour < CLOSE_HOURS - 24) {
            $dateShow->subDay();
        }
        if ($request->get('year') && $request->get('month')) {
            $year = $request->get('year');
            $month = $request->get('month');
            if ($month != $dateShow->format('m') || $year != $dateShow->format('Y')) {
                $dateMonth = Carbon::createFromFormat('Y-m', $year . '-' . $month);
                $dateShow = clone $dateMonth;
                $dateShow = $dateShow->endOfMonth();
                $dateStart = clone $dateMonth;
                $dateStart = $dateStart->startOfMonth();
            } else {
                $dateMonth = Carbon::createFromFormat('Y-m', $year . '-' . $month);
                $dateStart = clone $dateMonth;
                $dateStart = $dateMonth->startOfMonth();
            }
        } else {
            $dateMonth = Carbon::createFromFormat('Y-m', $dateShow->year . '-' . $dateShow->month);
            $dateStart = clone $dateMonth;
            $dateStart = $dateMonth->startOfMonth();
        }
        if ($dateStart < dateCanView()) {
            return redirect()->route('access.deny');
        }
        $dateEnd = clone $dateShow;

        $start = clone $dateStart;
        $end = clone $dateStart;
        $this->data['chartRevenue'] = [];
        while ($start <= $end->lastOfMonth()) {
            $this->data['chartRevenue'][$start->format('Y-m-d')] = 0;
            $start->addDay();
        }

        $this->data['date'] = $dateShow;
        $this->getRevenue($groupBy, $dateStart, $dateEnd);
        $this->data['revenueCourseGroupDate'] = $this->getArray($this->data['revenueCourseGroupDate'], $groupBy, 'course_id');
        $this->data['totalStatistic'] = $this->getTotalStatisticByTime($dateStart, $dateShow);
        $this->data['listOrderByCard'] = $this->orderRepository->listOrdersByCardInMonth($dateShow);

        return $this->renderView('admin.revenue.list_month');
    }

    public function getRevenue($groupBy, $dateStart, $dateEnd)
    {
        $this->data['totalCustomer'] = $this->orderRepository->getTotalCustomerPresentByTime($dateStart, $dateEnd);
        $this->data['totalRevenue'] = $this->orderRepository->getTotalRevenuePresentByTime($dateStart, $dateEnd) - $this->castCalendarRepository->getTotalPriceCastPresentByTime($dateStart, $dateEnd)->total;
        $revenueGroupDate = $this->orderRepository->getRevenueOrderGroupDate($dateStart, $dateEnd, $groupBy)->keyBy($groupBy);
        $this->data['revenueCourseGroupDate'] = $this->orderRepository->getCourseOrderGroupDate($dateStart, $dateEnd, $groupBy);
        $this->data['priceCastCalendarGroupDate'] = $this->castCalendarRepository->getPriceCastCalendarGroupDate($dateStart, $dateEnd, $groupBy)->keyBy($groupBy);
        $this->data['courses'] = $this->courseRepository->getAll();
        $totalRevenueMonth = 0;

        foreach ($revenueGroupDate as $dateStr => $revenue) {
            if ($groupBy == 'date') {
                $startDateStr = $dateStr;
                $endDateStr = $dateStr;
            } else {
                $startDateStr = $dateStr . '-01';
                $startOfMonth = Carbon::parse($startDateStr);
                $endOfMonth = $startOfMonth->lastOfMonth();
                $endDateStr = $endOfMonth->toDateString();
            }
            $startDate = Carbon::parse($startDateStr);
            $endDate = Carbon::parse($endDateStr);
            $revenue->price_orders_cash = $this->orderRepository->totalOrdersByCashByTime($startDateStr, $endDateStr)['total_price'];
            $revenue->price_paid_for_cast = $this->orderRepository->totalPriceForCastOrderByTime($startDateStr, $endDateStr);
            $revenue->price_dormitory = $this->castCalendarRepository->getTotalPriceCastPaiedByTime($startDate, $endDate)['total_dormitory_price'];
            $total_price_expenses = $this->orderRepository->getTotalPriceExpensesOrdersPaid($startDate, $endDate);
            $revenue->price_expenses = isset($total_price_expenses[0]->total_price_expenses) ? $total_price_expenses[0]->total_price_expenses : 0;
            $revenue->price_living = $this->castPriceLivingRepository->totalPriceByTime($startDateStr, $endDateStr);
            $revenue->price_decorate_room = $this->priceDecorateRoomRepository->getTotalPriceByTime($startDate, $endDate);
            $totalRevenueMonth += $revenue->total_price_cash_orders - $revenue->total_price_cast;
        }
        $this->data['totalRevenueMonth'] = $totalRevenueMonth;
        $this->data['revenueGroupDate'] = $revenueGroupDate;
        $arrDate = Arr::collapse([
            $this->data['revenueGroupDate']->pluck($groupBy),
            $this->data['priceCastCalendarGroupDate']->pluck($groupBy)
        ]);
        $arrDate = array_unique($arrDate);
        asort($arrDate);
        $this->data['arrDate'] = $arrDate;
    }

    private function getArray($data, $key1, $key2 = null)
    {
        $dataResult = [];
        if (!count($data)) return $dataResult;
        if ($key2) {
            foreach ($data as $item) {
                $dataResult[$item->{$key1}][$item->{$key2}] = $item;
            }
        } else {
            foreach ($data as $item) {
                $dataResult[$item->{$key1}] = $item;
            }
        }
        return $dataResult;
    }

    public function getStatisticForYear(Request $request)
    {
        $groupBy = 'month';
        $dateShow = Carbon::now();
        if ($dateShow->hour < CLOSE_HOURS - 24) {
            $dateShow->subDay();
        }
        if ($request->get('year')) {
            $year = $request->get('year');
            if ($year != $dateShow->format('Y')) {
                $dateShow = Carbon::createFromFormat('Y', $year);
                $dateShow = $dateShow->lastOfYear();
            }
        }
        $dateStart = Carbon::parse($dateShow->format('Y-01-01 00:00'));
        $dateEnd = clone $dateShow;
        if ($dateStart < dateCanView()) {
            return redirect()->route('access.deny');
        }
        $start = clone $dateStart;
        $end = clone $dateStart;
        $this->data['chartRevenue'] = [];
        while ($start <= $end->lastOfYear()) {
            $this->data['chartRevenue'][$start->format('Y-m')] = 0;
            $start->addMonth();
        }

        $this->data['date'] = $dateShow;
        $this->getRevenue($groupBy, $dateStart, $dateEnd);
        $this->data['revenueCourseGroupDate'] = $this->getArray($this->data['revenueCourseGroupDate'], $groupBy, 'course_id');
        $this->data['totalStatistic'] = $this->getTotalStatisticByTime($dateStart, $dateShow);

        return $this->renderView('admin.revenue.list_year');
    }

    /**
     * Optimized monthly statistics using daily_revenues table
     */
    public function getStatisticForMonthOptimized(Request $request)
    {
        $dateShow = Carbon::now();
        if ($dateShow->hour < CLOSE_HOURS - 24) {
            $dateShow->subDay();
        }

        if ($request->get('year') && $request->get('month')) {
            $year = $request->get('year');
            $month = $request->get('month');
            if ($month != $dateShow->format('m') || $year != $dateShow->format('Y')) {
                $dateMonth = Carbon::createFromFormat('Y-m', $year . '-' . $month);
                $dateShow = clone $dateMonth;
                $dateShow = $dateShow->endOfMonth();
                $dateStart = clone $dateMonth;
                $dateStart = $dateStart->startOfMonth();
            } else {
                $dateMonth = Carbon::createFromFormat('Y-m', $year . '-' . $month);
                $dateStart = clone $dateMonth;
                $dateStart = $dateMonth->startOfMonth();
            }
        } else {
            $dateMonth = Carbon::createFromFormat('Y-m', $dateShow->year . '-' . $dateShow->month);
            $dateStart = clone $dateMonth;
            $dateStart = $dateMonth->startOfMonth();
        }

        if ($dateStart < dateCanView()) {
            return redirect()->route('access.deny');
        }

        $dateEnd = clone $dateShow;

        // Get optimized data from daily_revenues table
        $monthlyData = $this->dailyRevenueRepository->getMonthlyData($dateStart->year, $dateStart->month);
        $dailyData = $this->dailyRevenueRepository->getByDateRange($dateStart, $dateEnd);

        $this->data['date'] = $dateShow;
        $this->data['totalCustomer'] = $monthlyData->total_customers ?? 0;
        $this->data['totalRevenueMonth'] = $monthlyData->net_revenue ?? 0;

        // Build chart data
        $start = clone $dateStart;
        $this->data['chartRevenue'] = [];
        while ($start <= $dateEnd) {
            $dailyRevenue = $dailyData->where('date', $start->toDateString())->first();
            $this->data['chartRevenue'][$start->format('Y-m-d')] = $dailyRevenue ? $dailyRevenue->net_revenue : 0;
            $start->addDay();
        }

        // Get aggregated statistics
        $this->data['totalStatistic'] = [
            'priceOrdersByCash' => $monthlyData->cash_orders_total_price ?? 0,
            'priceOrderForCast' => $monthlyData->total_cast_payments ?? 0,
            'costShop' => $monthlyData->total_cost_shop ?? 0,
            'priceForCast' => $monthlyData->price_for_cast ?? 0,
            'priceSupportByCash' => $monthlyData->cash_orders_support_price ?? 0,
            'priceOrdersByCard' => $monthlyData->card_orders_total_price ?? 0,
            'taxPriceOrdersByCard' => $monthlyData->card_orders_tax_price ?? 0,
            'numberOrdersByCard' => $monthlyData->card_orders_count ?? 0,
            'numberOrdersByCardInDay15' => $monthlyData->card_orders_in_day15_count ?? 0,
            'totalPriceOrdersByCardInDay15' => $monthlyData->card_orders_in_day15_total_price ?? 0,
            'taxPriceOrdersByCardInDay15' => $monthlyData->card_orders_in_day15_tax_price ?? 0,
            'priceCastOrdersByCardInDay15' => $monthlyData->card_orders_in_day15_cast_price ?? 0,
            'numberOrdersByCardInLastMonth' => $monthlyData->card_orders_last_month_count ?? 0,
            'totalPriceOrdersByCardInLastMonth' => $monthlyData->card_orders_last_month_total_price ?? 0,
            'taxPriceCastOrdersByCardInLastMonth' => $monthlyData->card_orders_last_month_tax_price ?? 0,
            'priceCastOrdersByCardInLastMonth' => $monthlyData->card_orders_last_month_cast_price ?? 0,
            'dormitoryPrice' => $monthlyData->total_dormitory_price ?? 0,
            'expenses' => $monthlyData->total_expenses ?? 0,
            'priceLiving' => $monthlyData->total_living_price ?? 0,
            'priceDecorateRoom' => $monthlyData->total_decorate_room_price ?? 0,
        ];

        // Get card orders for the month (fallback to legacy method for detailed list)
        $this->data['listOrderByCard'] = $this->orderRepository->listOrdersByCardInMonth($dateShow);

        return $this->renderView('admin.revenue.list_month');
    }

    /**
     * Optimized yearly statistics using daily_revenues table
     */
    public function getStatisticForYearOptimized(Request $request)
    {
        $dateShow = Carbon::now();
        if ($dateShow->hour < CLOSE_HOURS - 24) {
            $dateShow->subDay();
        }

        if ($request->get('year')) {
            $year = $request->get('year');
            if ($year != $dateShow->format('Y')) {
                $dateShow = Carbon::createFromFormat('Y', $year);
                $dateShow = $dateShow->lastOfYear();
            }
        }

        $dateStart = Carbon::parse($dateShow->format('Y-01-01 00:00'));
        $dateEnd = clone $dateShow;

        if ($dateStart < dateCanView()) {
            return redirect()->route('access.deny');
        }

        // Get optimized yearly data
        $yearlyData = $this->dailyRevenueRepository->getYearlyData($dateStart->year);

        $this->data['date'] = $dateShow;

        // Build chart data by month
        $start = clone $dateStart;
        $this->data['chartRevenue'] = [];
        while ($start <= $dateEnd) {
            $monthData = $yearlyData->where('month', $start->format('Y-m'))->first();
            $this->data['chartRevenue'][$start->format('Y-m')] = $monthData ? $monthData->net_revenue : 0;
            $start->addMonth();
        }

        // Aggregate yearly totals
        $totalCustomer = $yearlyData->sum('total_customers');
        $totalRevenue = $yearlyData->sum('net_revenue');

        $this->data['totalCustomer'] = $totalCustomer;
        $this->data['totalRevenueMonth'] = $totalRevenue;

        // Get aggregated statistics for the year
        $this->data['totalStatistic'] = [
            'priceOrdersByCash' => $yearlyData->sum('cash_orders_total_price'),
            'priceOrderForCast' => $yearlyData->sum('total_cast_payments'),
            'costShop' => $yearlyData->sum('total_cost_shop'),
            'priceForCast' => $yearlyData->sum('price_for_cast'),
            'priceSupportByCash' => $yearlyData->sum('cash_orders_support_price'),
            'priceOrdersByCard' => $yearlyData->sum('card_orders_total_price'),
            'taxPriceOrdersByCard' => $yearlyData->sum('card_orders_tax_price'),
            'numberOrdersByCard' => $yearlyData->sum('card_orders_count'),
            'dormitoryPrice' => $yearlyData->sum('total_dormitory_price'),
            'expenses' => $yearlyData->sum('total_expenses'),
            'priceLiving' => $yearlyData->sum('total_living_price'),
            'priceDecorateRoom' => $yearlyData->sum('total_decorate_room_price'),
        ];

        return $this->renderView('admin.revenue.list_year');
    }

    public function updateCostShop(Request $request)
    {
        $now = Carbon::now();
        if ($now->hour < CLOSE_HOURS - 24) {
            $now->subDay();
        }
        $date = $now->toDateString();
        if ($request->date) {
            $date = $request->date;
        }
        $listIds = array();
        $listItem = array();
        if ($request->name) {
            foreach ($request->name as $id => $name) {
                if ($name && $request->price[$id]) {
                    $item = [
                        [
                            'id'   => $id,
                        ],
                        [
                            'name'     => $name,
                            'price' => $request->price[$id],
                            'updated_by'    => Auth::user()->id
                        ]
                    ];
                    $listItem[] = $item;
                    $listIds[] = $id;
                }
            }
        }
        if ($request->name_item) {
            foreach ($request->name_item as $key => $name) {
                if ($name && $request->price_item[$key]) {
                    $item = array();
                    $item = [
                        'date' => $date,
                        'name' => $name,
                        'price' => $request->price_item[$key],
                        'created_by' => Auth::user()->id
                    ];
                    $listItem[] = $item;
                }
            }
        }
        DB::beginTransaction();
        try {
            $this->costShopRepository->deleteNotInIds($listIds, $date);
            $this->costShopRepository->updateOrCreate($listItem, $date);
            DB::commit();
            return redirect()->route('admin.revenue.list', ['date' => $date]);
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('alert-errors', 'データが適切ではありません。');
        }
    }
}
