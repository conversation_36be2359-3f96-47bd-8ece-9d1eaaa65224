<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CastCalendar;
use App\Models\Order;
use App\Repositories\CastCalendar\CastCalendarRepository;
use App\Repositories\CastPriceLiving\CastPriceLivingRepository;
use App\Repositories\CostShop\CostShopRepository;
use App\Repositories\Course\CourseRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\PriceDecorateRoom\PriceDecorateRoomRepository;
use App\Repositories\Room\RoomCalendarRepository;
use App\Repositories\Setting\SettingRepository;
use App\Repositories\DailyRevenue\DailyRevenueRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\DailyRevenue;

class RevenueController extends Controller
{
    private $castCalendarRepository;
    private $courseRepository;
    private $orderRepository;
    private $roomCalendarRepository;
    private $costShopRepository;
    private $castPriceLivingRepository;
    private $priceDecorateRoomRepository;
    private $settingRepository;
    private $dailyRevenueRepository;
    protected $data = [];

    public function __construct(
        CastCalendarRepository $castCalendarRepository,
        CourseRepository $courseRepository,
        OrderRepository $orderRepository,
        RoomCalendarRepository $roomCalendarRepository,
        CostShopRepository $costShopRepository,
        CastPriceLivingRepository $castPriceLivingRepository,
        PriceDecorateRoomRepository $priceDecorateRoomRepository,
        SettingRepository $settingRepository,
        DailyRevenueRepository $dailyRevenueRepository
    ) {
        $this->courseRepository = $courseRepository;
        $this->orderRepository = $orderRepository;
        $this->castCalendarRepository = $castCalendarRepository;
        $this->roomCalendarRepository = $roomCalendarRepository;
        $this->costShopRepository = $costShopRepository;
        $this->castPriceLivingRepository = $castPriceLivingRepository;
        $this->priceDecorateRoomRepository = $priceDecorateRoomRepository;
        $this->settingRepository = $settingRepository;
        $this->dailyRevenueRepository = $dailyRevenueRepository;
    }

    public function index(Request $request)
    {
        $date = Carbon::now();
        $dateShow = clone $date;
        $isToday = true;
        if (isset($request->date) && $request->date) {
            $date = Carbon::parse($request->date . ' ' . $date->format('H:i'));
            $dateShow = clone $date;
        } else if ($dateShow->hour < CLOSE_HOURS - 24) {
            $dateShow->subDay();
            $isToday = false;
        }

        if ($date < dateCanView()) {
            return redirect()->route('access.deny');
        }

        // Nếu không phải ngày hiện tại, thử lấy từ daily_revenues
        if ($this->loadFromDailyRevenue($dateShow, $isToday)) {
            $this->loadCommonData($dateShow, false);
            return $this->renderView('admin.revenue.list');
        }

        // Nếu là ngày hiện tại hoặc không có dữ liệu trong daily_revenues, lấy realtime
        $this->loadRealtimeData($date, $dateShow, $isToday);
        $this->loadCommonData($date, true);

        return $this->renderView('admin.revenue.list');
    }

    protected function loadFromDailyRevenue($date, $isToday = false)
    {
        $dailyRevenue = $this->dailyRevenueRepository->findByDate($date);
        if (!$dailyRevenue) {
            return false;
        }

        $setting = $this->settingRepository->getSetting();
        $taxPercentage = $setting->tax_percentage;
        $arrStatus = $this->getOrderStatuses();
        $filtersWork['arr_status'] = [CastCalendar::STATUS_PREPARE, CastCalendar::STATUS_WORKING];
        $totalCastCalendars = $this->castCalendarRepository->getCastCalendarSortStatus($date, $filtersWork, $isToday);

        // Get statistics for first 15 days
        $first15DaysStats = $this->dailyRevenueRepository->getFirst15DaysStatistics($date);

        $this->data = [
            'totalTypeFeeCast' => [],
            'totalMethodGroupFee' => [],
            'date' => $date,
            'totalCustomer' => $dailyRevenue->total_customers,
            'totalPriceCast' => $dailyRevenue->total_price_cast,
            'totalCourseCate' => $this->orderRepository->getTotalCourseCateByDateStatus($date, $arrStatus, $isToday),
            'minuteExtension' => $setting->minute_extension,
            'priceExtension' => $setting->price * (1 + $taxPercentage / 100),
            'priceCastExtension' => $setting->price_cast,
            'priceMinuteAdd' => $setting->price_minute_add,
            'dataOrders' => $this->orderRepository->getOrderPayOfDay($date->toDateString()),
            'totalCourseCateWorking' => $this->orderRepository->getTotalCourseCateByDateStatusPaid($date, $arrStatus, $isToday),
            'totalOrderByTypeFee' => $this->orderRepository->getTotalOrderTypeFee($date, $isToday)->keyBy('type_fee')->toArray(),
            'arrLabelTypeFee' => $this->orderRepository->getLabelTypeFees(),
            'totalOrderExtension' => (object)[
                'count_extension' => $dailyRevenue->extension_orders_count,
                'total_minute_extension' => $dailyRevenue->total_extension_minutes
            ],
            'castCalendars' => $totalCastCalendars,
            'castRoomCalendars' => $this->roomCalendarRepository->getListNameRoomCastByDate($date, $isToday)->keyBy('cast_id')->toArray(),
            'totalCast' => $dailyRevenue->total_cast,
            'totalCastWorking' => $dailyRevenue->total_cast_working,
            'listCostShop' => $this->costShopRepository->getAllByTime($date->toDateString(), $date->toDateString()),
            'totalStatistic' => [
                'priceForCast' => $dailyRevenue->total_price_for_cast,
                'dormitoryPrice' => $dailyRevenue->dormitory_price,
                'expenses' => $dailyRevenue->other_expenses,
                'priceLiving' => $dailyRevenue->living_expenses,
                'priceDecorateRoom' => $dailyRevenue->decorate_room_expenses,
                'numberOrdersByCard' => $dailyRevenue->card_orders_count,
                'priceOrdersByCard' => $dailyRevenue->card_orders_total,
                'taxPriceOrdersByCard' => $dailyRevenue->card_orders_tax,
                'priceCastOrdersByCard' => $dailyRevenue->card_orders_for_cast,
                'numberOrdersByCardInDay15' => $first15DaysStats->total_orders,
                'priceCastOrdersByCardInDay15' => $first15DaysStats->total_price_cast,
                'taxPriceOrdersByCardInDay15' => $first15DaysStats->total_tax,
                'totalPriceOrdersByCardInDay15' => $first15DaysStats->total_price,
                'numberOrdersByCardInLastMonth' => $dailyRevenue->card_orders_count - ($first15DaysStats->total_orders ?? 0),
                'priceCastOrdersByCardInLastMonth' => $dailyRevenue->card_orders_for_cast - ($first15DaysStats->total_price_cast ?? 0),
                'taxPriceCastOrdersByCardInLastMonth' => $dailyRevenue->card_orders_tax - ($first15DaysStats->total_tax ?? 0),
                'totalPriceOrdersByCardInLastMonth' => $dailyRevenue->card_orders_total - ($first15DaysStats->total_price ?? 0),
                'priceOrdersByCash' => $dailyRevenue->cash_orders_total,
                'priceCastNotSupportByCash' => $dailyRevenue->cash_orders_total_unsupported_for_cast,
                'priceSupportByCash' => $dailyRevenue->cash_orders_total_support_for_cast,
                'costShop' => $dailyRevenue->shop_costs,
                'totalRevenue' => $this->calculateTotalRevenue($dailyRevenue),
                'priceOrderForCast' => $this->orderRepository->totalPriceForCastOrderByTime($date->toDateString(), $date->toDateString()),
            ],
            'setting' => $setting
        ];

        return true;
    }

    private function loadRealtimeData($date, $dateShow, $isToday = true)
    {
        $arrStatus = $this->getOrderStatuses();
        $arrStatusRevenue = [
            Order::STATUS_HAPPENING,
            Order::STATUS_EXTENSION,
            Order::STATUS_FINISH
        ];

        $filtersWork['arr_status'] = [
            CastCalendar::STATUS_PREPARE,
            CastCalendar::STATUS_WORKING
        ];

        $filtersWorking['arr_status'] = [
            CastCalendar::STATUS_WORKING
        ];

        $filtersCast['arr_status'] = [
            CastCalendar::STATUS_WORKING,
            CastCalendar::STATUS_FINISH,
        ];

        $filtersCastCalendar['arr_status'] = [
            array_unique(array_merge($filtersCast['arr_status'], $filtersWork['arr_status'], $filtersWorking['arr_status'])),
        ];

        $totalCastCalendars = $this->castCalendarRepository->getCastCalendarSortStatus($date, $filtersCastCalendar, $isToday);

        $setting = $this->settingRepository->getSetting();
        $taxPercentage = $setting->tax_percentage;

        $this->data = [
            'totalTypeFeeCast' => [],
            'totalMethodGroupFee' => [],
            'date' => $dateShow,
            'totalCustomer' => $this->orderRepository->getTotalCustomerPresent($date, true, $isToday),
            'totalPriceCast' => $this->castCalendarRepository->getTotalPriceCastPresentByTime($date, $date, $isToday)->total,
            'totalCourseCate' => $this->orderRepository->getTotalCourseCateByDateStatus($date, $arrStatus, $isToday),
            'minuteExtension' => $setting->minute_extension,
            'priceExtension' => $setting->price * (1 + $taxPercentage / 100),
            'priceCastExtension' => $setting->price_cast,
            'priceMinuteAdd' => $setting->price_minute_add,
            'dataOrders' => $this->orderRepository->getOrderPayOfDay($dateShow->toDateString()),
            'totalCourseCateWorking' => $this->orderRepository->getTotalCourseCateByDateStatusPaid($date, $arrStatus, $isToday),
            'totalOrderByTypeFee' => $this->orderRepository->getTotalOrderTypeFee($date, $isToday)->keyBy('type_fee')->toArray(),
            'arrLabelTypeFee' => $this->orderRepository->getLabelTypeFees(),
            'totalOrderExtension' => $this->orderRepository->getTotalOrderExtension($date, $isToday),
            'castCalendars' => $totalCastCalendars->whereIn('status', $filtersWork['arr_status']),
            'castRoomCalendars' => $this->roomCalendarRepository->getListNameRoomCastByDate($date, $isToday)->keyBy('cast_id')->toArray(),
            'totalCast' => $totalCastCalendars->whereIn('status', $filtersCast['arr_status'])->count(),
            'totalCastWorking' => $totalCastCalendars->whereIn('status', $filtersWorking['arr_status'])->count(),
            'listCostShop' => $this->costShopRepository->getAllByTime($dateShow->toDateString(), $dateShow->toDateString()),
            'totalStatistic' => $this->getTotalStatisticByTime($dateShow, $dateShow),
            'setting' => $setting
        ];
    }

    private function loadCommonData($date, $isRealtime)
    {
        // Load orders và courses
        $this->data['dataOrders']->load('casts');
        $this->data['courses'] = $this->courseRepository->findCoursesWithTrashed(
            $this->data['dataOrders']->pluck('course_id')->unique()->toArray()
        );

        // Xử lý totalTypeFeeCast
        $dataTotalTypeFee = $this->orderRepository->getTotalTypeFeeOfCast($date, $isRealtime);
        if ($dataTotalTypeFee->count()) {
            foreach ($dataTotalTypeFee as $item) {
                $this->data['totalTypeFeeCast'][$item->cast_id][$item->type_fee] = $item;
            }
        }

        // Xử lý totalMethodGroupFee và totalMethodNetFee
        $totalMethodGroupFee = $this->orderRepository->getTotalMethodGroupFee($date, $isRealtime);
        if ($totalMethodGroupFee->count()) {
            foreach ($totalMethodGroupFee as $item) {
                if ($item->type_fee == Order::TYPE_NET) {
                    if (isset($this->data['totalMethodNetFee'][$item->method])) {
                        $this->data['totalMethodNetFee'][$item->method]++;
                    } else {
                        $this->data['totalMethodNetFee'][$item->method] = 1;
                    }
                }
            }
        }

        // Xử lý arrCountBookByStatus
        $this->data['arrCountBookByStatus'] = [];
        $countOrderBookByStatus = $this->orderRepository->countOrderByStatuses($this->getOrderStatuses(), $date)
            ->pluck('count_status', 'status');
        foreach ($this->getOrderStatuses() as $status) {
            $count = $countOrderBookByStatus->get($status);
            array_push($this->data['arrCountBookByStatus'], $count ? $count : 0);
        }
    }

    private function getOrderStatuses()
    {
        return [
            Order::STATUS_NO_HAPPEN_YET,
            Order::STATUS_HAPPENING,
            Order::STATUS_EXTENSION,
            Order::STATUS_FINISH
        ];
    }

    private function setTypeFeeData($date)
    {
        $dataTotalTypeFee = $this->orderRepository->getTotalTypeFeeOfCast($date, true);
        if ($dataTotalTypeFee->count()) {
            foreach ($dataTotalTypeFee as $item) {
                $this->data['totalTypeFeeCast'][$item->cast_id][$item->type_fee] = $item;
            }
        }

        $totalMethodGroupFee = $this->orderRepository->getTotalMethodGroupFee($date, true);
        if ($totalMethodGroupFee->count()) {
            foreach ($totalMethodGroupFee as $item) {
                if ($item->type_fee == Order::TYPE_NET) {
                    if (isset($this->data['totalMethodNetFee'][$item->method])) {
                        $this->data['totalMethodNetFee'][$item->method]++;
                    } else {
                        $this->data['totalMethodNetFee'][$item->method] = 1;
                    }
                }
            }
        }

        $this->data['arrCountBookByStatus'] = [];
        $countOrderBookByStatus = $this->orderRepository->countOrderByStatuses($this->getOrderStatuses(), $date)
            ->pluck('count_status', 'status');
        foreach ($this->getOrderStatuses() as $status) {
            $count = $countOrderBookByStatus->get($status);
            array_push($this->data['arrCountBookByStatus'], $count ? $count : 0);
        }
    }

    private function calculateTotalRevenue($dailyRevenue)
    {
        return $dailyRevenue->dormitory_price + $dailyRevenue->other_expenses +
            $dailyRevenue->living_expenses + $dailyRevenue->decorate_room_expenses -
            $dailyRevenue->cash_orders_total_unsupported_for_cast -
            $dailyRevenue->cash_orders_total_support_for_cast -
            $dailyRevenue->shop_costs;
    }

    public function getTotalStatisticByTime($dateStart, $dateEnd)
    {
        $totalPriceCastPaied = $this->castCalendarRepository->getTotalPriceCastPaiedByTime($dateStart, $dateEnd);
        $total_price_expenses = $this->orderRepository->getTotalPriceExpensesOrdersPaid($dateStart, $dateEnd);
        $totalStatistic['priceForCast'] = $totalPriceCastPaied->price_for_cast;
        $totalStatistic['dormitoryPrice'] = $totalPriceCastPaied->total_dormitory_price;
        $totalStatistic['expenses'] = isset($total_price_expenses[0]->total_price_expenses) ? $total_price_expenses[0]->total_price_expenses : 0;
        $totalStatistic['priceLiving'] = $this->castPriceLivingRepository->totalPriceByTime($dateStart->toDateString(), $dateEnd->toDateString());
        $totalStatistic['priceDecorateRoom'] = $this->priceDecorateRoomRepository->getTotalPriceByTime($dateStart, $dateEnd);
        $totalPriceOrderByCard = $this->orderRepository->totalOrdersByCardByTime($dateStart->toDateString(), $dateEnd->toDateString());
        $totalStatistic['numberOrdersByCard'] = $totalPriceOrderByCard['total'];
        $totalStatistic['priceOrdersByCard'] = $totalPriceOrderByCard['total_price'];
        $totalStatistic['taxPriceOrdersByCard'] = $totalPriceOrderByCard['total_tax_price'];
        $totalStatistic['priceCastOrdersByCard'] = $totalPriceOrderByCard['total_price_cast'];
        $totalPriceOrderByCardInDay15 = $this->orderRepository->totalOrdersByCardInDay15ByTime($dateStart, $dateEnd);
        $totalStatistic['numberOrdersByCardInDay15'] = $totalPriceOrderByCardInDay15['total'];
        $totalStatistic['priceCastOrdersByCardInDay15'] = $totalPriceOrderByCardInDay15['total_price_cast'];
        $totalStatistic['taxPriceOrdersByCardInDay15'] = $totalPriceOrderByCardInDay15['total_tax_price'];
        $totalStatistic['totalPriceOrdersByCardInDay15'] = $totalPriceOrderByCardInDay15['total_price'];
        $totalStatistic['numberOrdersByCardInLastMonth'] = $totalPriceOrderByCard['total'] - $totalPriceOrderByCardInDay15['total'];
        $totalStatistic['priceCastOrdersByCardInLastMonth'] = $totalPriceOrderByCard['total_price_cast'] - $totalPriceOrderByCardInDay15['total_price_cast'];
        $totalStatistic['taxPriceCastOrdersByCardInLastMonth'] = $totalPriceOrderByCard['total_tax_price'] - $totalPriceOrderByCardInDay15['total_tax_price'];
        $totalStatistic['totalPriceOrdersByCardInLastMonth'] = $totalPriceOrderByCard['total_price'] - $totalPriceOrderByCardInDay15['total_price'];
        $totalPriceOrderByCash = $this->orderRepository->totalOrdersByCashByTime($dateStart->toDateString(), $dateEnd->toDateString());
        $totalStatistic['priceOrdersByCash'] = $totalPriceOrderByCash['total_price'];
        $totalStatistic['priceCastNotSupportByCash'] = $totalPriceOrderByCash['total_price_cast_not_support'];
        $totalStatistic['priceSupportByCash'] = $totalPriceOrderByCash['total_price_support'];
        $totalStatistic['costShop'] = $this->costShopRepository->totalPriceByTime($dateStart->toDateString(), $dateEnd->toDateString());
        $totalStatistic['totalRevenue'] = $totalStatistic['dormitoryPrice'] + $totalStatistic['expenses'] + $totalStatistic['priceLiving'] + $totalStatistic['priceDecorateRoom'] - $totalStatistic['priceCastNotSupportByCash'] - $totalStatistic['priceSupportByCash'] - $totalStatistic['costShop'];
        $totalStatistic['priceOrderForCast'] = $this->orderRepository->totalPriceForCastOrderByTime($dateStart->toDateString(), $dateEnd->toDateString());
        return $totalStatistic;
    }

    public function getStatisticForMonth(Request $request)
    {
        $groupBy = 'date';
        $dateShow = Carbon::now();
        if ($dateShow->hour < CLOSE_HOURS - 24) {
            $dateShow->subDay();
        }
        if ($request->get('year') && $request->get('month')) {
            $year = $request->get('year');
            $month = $request->get('month');
            if ($month != $dateShow->format('m') || $year != $dateShow->format('Y')) {
                $dateMonth = Carbon::createFromFormat('Y-m', $year . '-' . $month);
                $dateShow = clone $dateMonth;
                $dateShow = $dateShow->endOfMonth();
                $dateStart = clone $dateMonth;
                $dateStart = $dateStart->startOfMonth();
            } else {
                $dateMonth = Carbon::createFromFormat('Y-m', $year . '-' . $month);
                $dateStart = clone $dateMonth;
                $dateStart = $dateMonth->startOfMonth();
            }
        } else {
            $dateMonth = Carbon::createFromFormat('Y-m', $dateShow->year . '-' . $dateShow->month);
            $dateStart = clone $dateMonth;
            $dateStart = $dateMonth->startOfMonth();
        }
        if ($dateStart < dateCanView()) {
            return redirect()->route('access.deny');
        }
        $dateEnd = clone $dateShow;

        $start = clone $dateStart;
        $end = clone $dateStart;
        $this->data['chartRevenue'] = [];
        while ($start <= $end->lastOfMonth()) {
            $this->data['chartRevenue'][$start->format('Y-m-d')] = 0;
            $start->addDay();
        }

        $this->data['date'] = $dateShow;
        $this->getRevenue($groupBy, $dateStart, $dateEnd);
        $this->data['revenueCourseGroupDate'] = $this->getArray($this->data['revenueCourseGroupDate'], $groupBy, 'course_id');
        $this->data['totalStatistic'] = $this->getTotalStatisticByTime($dateStart, $dateShow);
        $this->data['listOrderByCard'] = $this->orderRepository->listOrdersByCardInMonth($dateShow);

        return $this->renderView('admin.revenue.list_month');
    }

    public function getRevenue($groupBy, $dateStart, $dateEnd)
    {
        $this->data['totalCustomer'] = $this->orderRepository->getTotalCustomerPresentByTime($dateStart, $dateEnd);
        $this->data['totalRevenue'] = $this->orderRepository->getTotalRevenuePresentByTime($dateStart, $dateEnd) - $this->castCalendarRepository->getTotalPriceCastPresentByTime($dateStart, $dateEnd)->total;
        $revenueGroupDate = $this->orderRepository->getRevenueOrderGroupDate($dateStart, $dateEnd, $groupBy)->keyBy($groupBy);
        $this->data['revenueCourseGroupDate'] = $this->orderRepository->getCourseOrderGroupDate($dateStart, $dateEnd, $groupBy);
        $this->data['priceCastCalendarGroupDate'] = $this->castCalendarRepository->getPriceCastCalendarGroupDate($dateStart, $dateEnd, $groupBy)->keyBy($groupBy);
        $this->data['courses'] = $this->courseRepository->getAll();
        $totalRevenueMonth = 0;

        foreach ($revenueGroupDate as $dateStr => $revenue) {
            if ($groupBy == 'date') {
                $startDateStr = $dateStr;
                $endDateStr = $dateStr;
            } else {
                $startDateStr = $dateStr . '-01';
                $startOfMonth = Carbon::parse($startDateStr);
                $endOfMonth = $startOfMonth->lastOfMonth();
                $endDateStr = $endOfMonth->toDateString();
            }
            $startDate = Carbon::parse($startDateStr);
            $endDate = Carbon::parse($endDateStr);
            $revenue->price_orders_cash = $this->orderRepository->totalOrdersByCashByTime($startDateStr, $endDateStr)['total_price']; // cash_orders_total
            $revenue->price_paid_for_cast = $this->orderRepository->totalPriceForCastOrderByTime($startDateStr, $endDateStr);
            $revenue->price_dormitory = $this->castCalendarRepository->getTotalPriceCastPaiedByTime($startDate, $endDate)['total_dormitory_price']; // dormitory_price
            $total_price_expenses = $this->orderRepository->getTotalPriceExpensesOrdersPaid($startDate, $endDate); // other_expenses
            $revenue->price_expenses = isset($total_price_expenses[0]->total_price_expenses) ? $total_price_expenses[0]->total_price_expenses : 0; // other_expenses
            $revenue->price_living = $this->castPriceLivingRepository->totalPriceByTime($startDateStr, $endDateStr); // living_expenses
            $revenue->price_decorate_room = $this->priceDecorateRoomRepository->getTotalPriceByTime($startDate, $endDate); // decorate_room_expenses
            $totalRevenueMonth += $revenue->total_price_cash_orders - $revenue->total_price_cast;
        }
        $this->data['totalRevenueMonth'] = $totalRevenueMonth;
        $this->data['revenueGroupDate'] = $revenueGroupDate;
        $arrDate = Arr::collapse([
            $this->data['revenueGroupDate']->pluck($groupBy),
            $this->data['priceCastCalendarGroupDate']->pluck($groupBy)
        ]);
        $arrDate = array_unique($arrDate);
        asort($arrDate);
        $this->data['arrDate'] = $arrDate;
    }

    private function getArray($data, $key1, $key2 = null)
    {
        $dataResult = [];
        if (!count($data)) return $dataResult;
        if ($key2) {
            foreach ($data as $item) {
                $dataResult[$item->{$key1}][$item->{$key2}] = $item;
            }
        } else {
            foreach ($data as $item) {
                $dataResult[$item->{$key1}] = $item;
            }
        }
        return $dataResult;
    }

    public function getStatisticForYear(Request $request)
    {
        $groupBy = 'month';
        $dateShow = Carbon::now();
        if ($dateShow->hour < CLOSE_HOURS - 24) {
            $dateShow->subDay();
        }
        if ($request->get('year')) {
            $year = $request->get('year');
            if ($year != $dateShow->format('Y')) {
                $dateShow = Carbon::createFromFormat('Y', $year);
                $dateShow = $dateShow->lastOfYear();
            }
        }
        $dateStart = Carbon::parse($dateShow->format('Y-01-01 00:00'));
        $dateEnd = clone $dateShow;
        if ($dateStart < dateCanView()) {
            return redirect()->route('access.deny');
        }
        $start = clone $dateStart;
        $end = clone $dateStart;
        $this->data['chartRevenue'] = [];
        while ($start <= $end->lastOfYear()) {
            $this->data['chartRevenue'][$start->format('Y-m')] = 0;
            $start->addMonth();
        }

        $this->data['date'] = $dateShow;
        $this->getRevenue($groupBy, $dateStart, $dateEnd);
        $this->data['revenueCourseGroupDate'] = $this->getArray($this->data['revenueCourseGroupDate'], $groupBy, 'course_id');
        $this->data['totalStatistic'] = $this->getTotalStatisticByTime($dateStart, $dateShow);

        return $this->renderView('admin.revenue.list_year');
    }

    public function updateCostShop(Request $request)
    {
        $now = Carbon::now();
        if ($now->hour < CLOSE_HOURS - 24) {
            $now->subDay();
        }
        $date = $now->toDateString();
        if ($request->date) {
            $date = $request->date;
        }
        $listIds = array();
        $listItem = array();
        if ($request->name) {
            foreach ($request->name as $id => $name) {
                if ($name && $request->price[$id]) {
                    $item = [
                        [
                            'id'   => $id,
                        ],
                        [
                            'name'     => $name,
                            'price' => $request->price[$id],
                            'updated_by'    => Auth::user()->id
                        ]
                    ];
                    $listItem[] = $item;
                    $listIds[] = $id;
                }
            }
        }
        if ($request->name_item) {
            foreach ($request->name_item as $key => $name) {
                if ($name && $request->price_item[$key]) {
                    $item = array();
                    $item = [
                        'date' => $date,
                        'name' => $name,
                        'price' => $request->price_item[$key],
                        'created_by' => Auth::user()->id
                    ];
                    $listItem[] = $item;
                }
            }
        }
        DB::beginTransaction();
        try {
            $this->costShopRepository->deleteNotInIds($listIds, $date);
            $this->costShopRepository->updateOrCreate($listItem, $date);
            DB::commit();
            return redirect()->route('admin.revenue.list', ['date' => $date]);
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('alert-errors', 'データが適切ではありません。');
        }
    }
}
