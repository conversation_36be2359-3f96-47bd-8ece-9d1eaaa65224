<?php

namespace App\Constants;

class Nav
{
    const MENUS = [
        [
            'icon' => 'icon-general',
            'url' => 'admin.general.manage',
            'name' => '総合管理',
            'page' => 'general'
        ],
        [
            'icon' => 'icon-status',
            'url' => 'admin.manage.status',
            'name' => '現状管理',
            'page' => 'status_order'
        ],
        [
            'icon' => 'icon-timetable',
            'url' => 'admin.timetable.index',
            'name' => 'タイムテーブル',
            'page' => 'timeTable'
        ],
        [
            'icon' => 'icon-booking',
            'url' => 'admin.booking.cast',
            'name' => '予約受付',
            'page' => 'booking'
        ],
        [
            'icon' => 'icon-daskboard',
            'url' => 'admin.home.index',
            'name' => '管理トップ',
            'page' => 'home'
        ],
        [
            'icon' => 'icon-cast',
            'url' => 'admin.cast.index',
            'name' => 'キャスト管理',
            'page' => 'cast'
        ],
        [
            'icon' => 'icon-attendance',
            'url' => 'admin.cast_calendar.list',
            'name' => '出勤 / 清算管理',
            'page' => 'cast_calendar'
        ],
        [
            'icon' => 'icon-course',
            'url' => 'admin.course.index',
            'name' => 'コース管理',
            'page' => 'course'
        ],
        [
            'icon' => 'icon-coupon',
            'url' => 'admin.coupon.index',
            'name' => '割引管理',
            'page' => 'coupon'
        ],
        [
            'icon' => 'icon-option',
            'url' => 'admin.option.index',
            'name' => 'オプション管理',
            'page' => 'option'
        ],
        [
            'icon' => 'icon-customer',
            'url' => 'admin.member.list',
            'name' => '顧客管理',
            'page' => 'member'
        ],
        [
            'icon' => 'icon-revenue',
            'url' => 'admin.revenue.list',
            'name' => '売上管理',
            'page' => 'revenue'
        ],
        [
            'icon' => 'icon-room',
            'url' => 'admin.calendar.index',
            'name' => '部屋割り管理',
            'page' => 'room_calendar'
        ],
        [
            'icon' => 'icon-setting',
            'url' => 'admin.setting.index',
            'name' => '設定管理',
            'page' => 'setting'
        ],
        [
            'icon' => 'icon-list-user',
            'url' => 'admin.users.index',
            'name' => 'ユーザー管理',
            'page' => 'users'
        ],
    ];

    const HOME_LEFT = [
        [
            'url' => 'admin.timetable.index',
            'name' => 'タイムテーブル',
            'class' => 'manage-20',
            'page' => 'timeTable',
        ],
        [
            'url' => 'admin.manage.status',
            'name' => '現状管理',
            'class' => 'manage-15',
            'page' => 'status_order',
        ],
        [
            'url' => 'admin.booking.cast',
            'name' => '予約受付管理',
            'class' => 'manage-15',
            'page' => 'booking',
        ],
        [
            'url' => 'admin.member.list',
            'name' => '顧客管理',
            'page' => 'member',
        ],
        [
            'url' => 'admin.calendar.index',
            'name' => '部屋割り管理',
            'page' => 'room_calendar',
        ],
    ];

    const HOME_RIGHT = [
        [
            'url' => 'admin.cast.index',
            'name' => 'キャスト管理',
            'page' => 'cast',
        ],
        [
            'url' => 'admin.cast_calendar.list',
            'name' => '出勤／清算管理',
            'page' => 'cast_calendar',
        ],
        [
            'url' => 'admin.course.index',
            'name' => 'コース管理',
            'page' => 'course',
        ],
        [
            'url' => 'admin.coupon.index',
            'name' => '割引管理',
            'page' => 'coupon',
        ],
        [
            'url' => 'admin.option.index',
            'name' => 'オプション管理',
            'page' => 'option',
        ],
        [
            'url' => 'admin.revenue.list',
            'name' => '売上管理',
            'page' => 'revenue',
        ],
        [
            'url' => 'admin.setting.index',
            'name' => '設定管理',
            'page' => 'setting',
        ],
    ];
}
