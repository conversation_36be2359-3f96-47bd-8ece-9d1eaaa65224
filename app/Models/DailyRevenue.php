<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class DailyRevenue extends Model
{
    protected $fillable = [
        'date',
        'total_orders_count',
        'total_revenue',
        'total_cash_revenue',
        'total_card_revenue',
        'total_cast_payments',
        'total_dormitory_price',
        'total_expenses',
        'price_for_cast',
        'total_living_price',
        'total_decorate_room_price',
        'total_cost_shop',
        'total_customers',
        'card_orders_count',
        'card_orders_total_price',
        'card_orders_tax_price',
        'card_orders_cast_price',
        'card_orders_in_day15_count',
        'card_orders_in_day15_total_price',
        'card_orders_in_day15_tax_price',
        'card_orders_in_day15_cast_price',
        'card_orders_last_month_count',
        'card_orders_last_month_total_price',
        'card_orders_last_month_tax_price',
        'card_orders_last_month_cast_price',
        'cash_orders_total_price',
        'cash_orders_cast_not_support',
        'cash_orders_support_price',
        'revenue_by_course',
        'revenue_by_type_fee',
        'net_revenue',
        'gross_profit'
    ];

    protected $casts = [
        'date' => 'date',
        'revenue_by_course' => 'array',
        'revenue_by_type_fee' => 'array',
        'total_revenue' => 'decimal:2',
        'total_cash_revenue' => 'decimal:2',
        'total_card_revenue' => 'decimal:2',
        'total_cast_payments' => 'decimal:2',
        'total_dormitory_price' => 'decimal:2',
        'total_expenses' => 'decimal:2',
        'price_for_cast' => 'decimal:2',
        'total_living_price' => 'decimal:2',
        'total_decorate_room_price' => 'decimal:2',
        'total_cost_shop' => 'decimal:2',
        'card_orders_total_price' => 'decimal:2',
        'card_orders_tax_price' => 'decimal:2',
        'card_orders_cast_price' => 'decimal:2',
        'card_orders_in_day15_total_price' => 'decimal:2',
        'card_orders_in_day15_tax_price' => 'decimal:2',
        'card_orders_in_day15_cast_price' => 'decimal:2',
        'card_orders_last_month_total_price' => 'decimal:2',
        'card_orders_last_month_tax_price' => 'decimal:2',
        'card_orders_last_month_cast_price' => 'decimal:2',
        'cash_orders_total_price' => 'decimal:2',
        'cash_orders_cast_not_support' => 'decimal:2',
        'cash_orders_support_price' => 'decimal:2',
        'net_revenue' => 'decimal:2',
        'gross_profit' => 'decimal:2'
    ];

    /**
     * Get revenue data for a specific date
     */
    public static function getByDate($date)
    {
        if ($date instanceof Carbon) {
            $date = $date->toDateString();
        }
        
        return static::where('date', $date)->first();
    }

    /**
     * Get revenue data for a date range
     */
    public static function getByDateRange($startDate, $endDate)
    {
        if ($startDate instanceof Carbon) {
            $startDate = $startDate->toDateString();
        }
        if ($endDate instanceof Carbon) {
            $endDate = $endDate->toDateString();
        }
        
        return static::whereBetween('date', [$startDate, $endDate])
                    ->orderBy('date')
                    ->get();
    }

    /**
     * Get monthly aggregated data
     */
    public static function getMonthlyData($year, $month)
    {
        $startDate = Carbon::createFromFormat('Y-m', $year . '-' . sprintf('%02d', $month))->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();
        
        return static::whereBetween('date', [$startDate->toDateString(), $endDate->toDateString()])
                    ->selectRaw('
                        SUM(total_orders_count) as total_orders_count,
                        SUM(total_revenue) as total_revenue,
                        SUM(total_cash_revenue) as total_cash_revenue,
                        SUM(total_card_revenue) as total_card_revenue,
                        SUM(total_cast_payments) as total_cast_payments,
                        SUM(total_dormitory_price) as total_dormitory_price,
                        SUM(total_expenses) as total_expenses,
                        SUM(price_for_cast) as price_for_cast,
                        SUM(total_living_price) as total_living_price,
                        SUM(total_decorate_room_price) as total_decorate_room_price,
                        SUM(total_cost_shop) as total_cost_shop,
                        SUM(total_customers) as total_customers,
                        SUM(card_orders_count) as card_orders_count,
                        SUM(card_orders_total_price) as card_orders_total_price,
                        SUM(card_orders_tax_price) as card_orders_tax_price,
                        SUM(card_orders_cast_price) as card_orders_cast_price,
                        SUM(card_orders_in_day15_count) as card_orders_in_day15_count,
                        SUM(card_orders_in_day15_total_price) as card_orders_in_day15_total_price,
                        SUM(card_orders_in_day15_tax_price) as card_orders_in_day15_tax_price,
                        SUM(card_orders_in_day15_cast_price) as card_orders_in_day15_cast_price,
                        SUM(card_orders_last_month_count) as card_orders_last_month_count,
                        SUM(card_orders_last_month_total_price) as card_orders_last_month_total_price,
                        SUM(card_orders_last_month_tax_price) as card_orders_last_month_tax_price,
                        SUM(card_orders_last_month_cast_price) as card_orders_last_month_cast_price,
                        SUM(cash_orders_total_price) as cash_orders_total_price,
                        SUM(cash_orders_cast_not_support) as cash_orders_cast_not_support,
                        SUM(cash_orders_support_price) as cash_orders_support_price,
                        SUM(net_revenue) as net_revenue,
                        SUM(gross_profit) as gross_profit
                    ')
                    ->first();
    }

    /**
     * Get yearly aggregated data
     */
    public static function getYearlyData($year)
    {
        $startDate = Carbon::createFromFormat('Y', $year)->startOfYear();
        $endDate = $startDate->copy()->endOfYear();
        
        return static::whereBetween('date', [$startDate->toDateString(), $endDate->toDateString()])
                    ->selectRaw('
                        DATE_FORMAT(date, "%Y-%m") as month,
                        SUM(total_orders_count) as total_orders_count,
                        SUM(total_revenue) as total_revenue,
                        SUM(total_cash_revenue) as total_cash_revenue,
                        SUM(total_card_revenue) as total_card_revenue,
                        SUM(total_cast_payments) as total_cast_payments,
                        SUM(total_dormitory_price) as total_dormitory_price,
                        SUM(total_expenses) as total_expenses,
                        SUM(price_for_cast) as price_for_cast,
                        SUM(total_living_price) as total_living_price,
                        SUM(total_decorate_room_price) as total_decorate_room_price,
                        SUM(total_cost_shop) as total_cost_shop,
                        SUM(total_customers) as total_customers,
                        SUM(card_orders_count) as card_orders_count,
                        SUM(card_orders_total_price) as card_orders_total_price,
                        SUM(card_orders_tax_price) as card_orders_tax_price,
                        SUM(card_orders_cast_price) as card_orders_cast_price,
                        SUM(net_revenue) as net_revenue,
                        SUM(gross_profit) as gross_profit
                    ')
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get();
    }

    /**
     * Calculate derived fields before saving
     */
    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($model) {
            // Calculate net revenue
            $model->net_revenue = $model->total_revenue - $model->total_cast_payments;
            
            // Calculate gross profit
            $model->gross_profit = $model->total_dormitory_price + 
                                 $model->total_expenses + 
                                 $model->total_living_price + 
                                 $model->total_decorate_room_price;
        });
    }
}
