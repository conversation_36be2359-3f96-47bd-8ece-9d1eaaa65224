<?php

use App\Constants\Orders;
use App\Models\Setting;
use App\Services\OrderService;

function calculateOrderTotalPrice($order)
{
    $setting = Setting::orderByDesc('id')->first();
    if (!$order || !$setting) {
        return 0;
    }
    $tax = $order->tax_percentage ?? OrderService::PERCENT;
    $pricePoint = $order->price_point ?? $setting->price_point;
    $extensionPrice = ($order->price_extension ?? $setting->price) * (1 + $tax / 100);
    $extensionMinute = $order->minute_extension_setting ?? $setting->minute_extension;
    // order total price = course price + fee price + option price + extension price - coupon price
    $orderTotalPrice = $order->course_price
        + $order->price_fee_new
        + $order->option_parent_price
        + $order->option_sub_price
        - ($order->coupon_1_price + $order->coupon_2_price + $order->coupon_3_price);
    // if use extension minute
    if ($order->minute_extension && $extensionMinute) {
        $orderTotalPrice += $extensionPrice * ($order->minute_extension / $extensionMinute);
    }
    // if use point
    if ($order->point && $pricePoint) {
        $orderTotalPrice -= floor($order->point / $pricePoint);
    }
    //  order total price
    $orderTotalPrice = $orderTotalPrice > 0 ? $orderTotalPrice : 0;
    return (int) $orderTotalPrice;
}

function calculateCustomerPayOrderTotalPrice($order)
{
    $setting = Setting::orderByDesc('id')->first();
    if (!$order || !$setting) {
        return 0;
    }
    $tax = $order->tax_percentage ?? OrderService::PERCENT;
    $pricePoint = $order->price_point ?? $setting->price_point;
    $extensionPrice = ($order->price_extension ?? $setting->price) * (1 + $tax / 100);
    $extensionMinute = $order->minute_extension_setting ?? $setting->minute_extension;
    // customer pay order total price = (course price + fee price + option price - coupon price) * (1 + percent if payment card) + extension price
    $customerPayOrderTotalPrice = $order->course_price
        + $order->price_fee_new
        + $order->option_parent_price
        + $order->option_sub_price
        - ($order->coupon_1_price + $order->coupon_2_price + $order->coupon_3_price);
    // if use point
    if ($order->point && $pricePoint) {
        $customerPayOrderTotalPrice -= floor($order->point / $pricePoint);
    }
    // if payment card
    if ($order->type_pay == Orders::TYPE_PAYMENT_CARD) {
        $customerPayOrderTotalPrice *= 1 + OrderService::PERCENT / 100;
    }
    // extension price not include fee payment card
    // if use extension minute
    if ($order->minute_extension && $extensionMinute) {
        $customerPayOrderTotalPrice += $extensionPrice * ($order->minute_extension / $extensionMinute);
    }

    // total order price customer pay
    $customerPayOrderTotalPrice = $customerPayOrderTotalPrice > 0 ? $customerPayOrderTotalPrice : 0;
    return (int) $customerPayOrderTotalPrice;
}
