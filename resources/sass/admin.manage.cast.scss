@import "variables";
.manage-cast {
    .group-button-top {
        display: flex;
        flex-direction: column;
        .btn-group {
            margin: 10px 0px 10px 0px;
            .btn-1 {
                max-width: 170px;
            }
            .btn-2 {
                max-width: 200px;
            }
        }
    }
    .form-inline {
        display: flex;
        flex-flow: row wrap;
        align-items: flex-end;
        text-align: left;
        flex-direction: column;
        margin-bottom: 2%;
        .button-default {
            margin-left: 5px;
            padding: 5px 0;
            width: 100px;
        }
        .find-cast {
            color: $black;
            font-size: 12px;
        }
        .search-list-cast {
            display: flex;
            width: 100%;
            justify-content: flex-end;
            label {
                width: 20%;
                font-size: 14px;
            }
            input {
                width: 40%;
                height: calc(0.6em + 1.25rem + 5px);
                font-size: 12px;
                color: $black;
            }
            .button-search {
                width: 7em;
                padding: 0.25em;
                border: 2px solid #2dce89;
                background-color: #2dce89;
                margin-left: 5px;
                color: white;
                font-size: .875rem;
            }
        }
        #listCast {
            position: absolute;
            margin: 32px 103px 0 0;
            z-index: 1000;
            display: flex;
            justify-content: flex-end;
            .dropdown-menu {
                min-width: 72%;
                font-size: 13px;
                border-radius: 0px 0px 5px 5px;
                margin: 0;
                a {
                    color: $black;
                    font-weight: 400;
                }
                li {
                    padding: 5px 12px;
                }
                li:hover {
                    background-color: $colorBorderTable;
                }
            }
        }
    }
    .filter {
        .btn {
            font-weight: 400;
            color: white;
            border-radius: 25px;
        }
        .filter-1 {
            background-color: $yellow1;
        }
        .filter-2 {
            background-color: $orange;
        }
        button:active,
        .active {
            background-color: $yellow;
        }
    }
    .list-cast {
        display: flex;
        .note {
            color: $red;
            text-align: center;
            width: 100%;
            margin-top: 5%;
        }
        .hashtag {
            width: auto;
            display: flex;
            align-items: center;
            align-content: center;
            margin-top: 30px;
            div {
                height: 24px;
                font-size: 0.6rem;
                margin-right: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 5px;
                color: $black;
            }
        }
        .hashtag-1 {
            background-color: $white;
            border: 2px solid $red;
            padding: 0 6px;
        }
        .hashtag-2 {
            background-color: $white;
            border: 2px solid $green;
            padding: 0 6px;
        }
        .hashtag-3 {
            background-color: $white;
            border: 2px solid $purple;
            padding: 0 6px;
        }
        .cast-item-1 {
            width: 100%;
            height: 400px;
            margin: 2px 0 0 0;
            box-shadow: 0px 4px 12px -4px rgb(0 0 0 / 19%);
            border-radius: 15px;
        }
        .img-cast {
            height: 300px;
            width: 100%;
            object-fit: cover;
            border-radius: 15px 15px 0px 0px;
        }
        .image-cast {
            .disabled {
                opacity: 0.4;
            }
        }
        .information-cast {
            margin: 10px 5px;
            p {
                font-size: 12px;
                margin: 0;
                height: 30px;
            }
        }
        .body-measurements {
            display: flex;
            p {
                margin-right: 5px;
            }
        }
        .button-control {
            .label-status {
                background-color: $pink;
                width: 100%;
                text-align: center;
                border-radius: 10px;
                border: 1px solid $black;
                margin: 5px 0px 5px 0px;
                color: $black;
                font-size: 14px;
                height: 30px;
                display: flex;
                align-items: center;
                align-content: center;
                justify-content: center;
            }
            .btn-ct-primary {
                width: 49%;
                padding: 6px 0px;
                font-size: 10px;
                margin: 0;
            }
            .btn.disabled,
            .btn:disabled {
                opacity: 1;
            }
        }
        .disabled {
            background-color: $disabled !important;
        }
    }
    .search-filter {
        margin-top: 10px;
        .form-search {
            margin-top: 5px;
        }
    }
}

@media screen and (max-width: 2560px) {
    .manage-cast {
        #listCast {
            min-width: 54%;
        }
        .hashtag div {
            font-size: 15px !important;
        }
        .button-control {
            .label-status {
                font-size: 20px !important;
            }
            .btn-ct-primary {
                font-size: 16px !important;
            }
        }
    }
}

@media screen and (max-width: 1900px) {
    .manage-cast {
        #listCast {
            min-width: 54%;
        }
        .hashtag div {
            font-size: 13px !important;
        }
        .button-control {
            .label-status {
                font-size: 18px !important;
            }
            .btn-ct-primary {
                font-size: 14px !important;
            }
        }
    }
}

@media screen and (max-width: 1900px) {
    .manage-cast {
        #listCast {
            min-width: 54%;
        }
        .hashtag div {
            font-size: 11px !important;
        }
        .button-control {
            .label-status {
                font-size: 16px !important;
            }
            .btn-ct-primary {
                font-size: 12px !important;
            }
        }
    }
}

@media screen and (max-width: 1500px) {
    .manage-cast {
        #listCast {
            min-width: 53%;
        }
        .hashtag div {
            font-size: 10px !important;
        }
        .button-control {
            .label-status {
                font-size: 14px !important;
            }
            .btn-ct-primary {
                font-size: 10px !important;
            }
        }
    }
}

@media screen and (max-width: 1330px) {
    .manage-cast {
        #listCast {
            min-width: 52%;
        }
        .hashtag div {
            font-size: 9px !important;
        }
        .button-control {
            .label-status {
                font-size: 12px !important;
            }
            .btn-ct-primary {
                font-size: 9px !important;
            }
        }
        .information-cast p {
            font-size: 11px !important;
        }
    }
}

@media screen and (max-width: 1260px) {
    .manage-cast {
        #listCast {
            min-width: 52%;
        }
        .hashtag div {
            font-size: 8px !important;
        }
        .button-control {
            .label-status {
                font-size: 12px !important;
            }
            .btn-ct-primary {
                font-size: 8px !important;
                width: 48% !important;
            }
        }
        .information-cast p {
            font-size: 10px !important;
        }
    }
}

@media screen and (max-width: 1185px) {
    .manage-cast {
        #listCast {
            min-width: 51%;
        }
        .hashtag div {
            font-size: 7px !important;
            padding: 0px 2px !important;
        }
        .button-control {
            .label-status {
                font-size: 12px !important;
            }
            .btn-ct-primary {
                font-size: 7px !important;
                width: 48% !important;
            }
        }
        .information-cast p {
            font-size: 10px !important;
        }
    }
}

@media screen and (max-width: 1030px) {
    .manage-cast {
        #listCast {
            min-width: 51%;
        }
        .hashtag div {
            font-size: 7px !important;
            padding: 0px !important;
            margin: 0 !important;
        }
        .button-control {
            .label-status {
                font-size: 12px !important;
            }
            .btn-ct-primary {
                font-size: 6px !important;
                width: 48% !important;
            }
        }
        .information-cast p {
            font-size: 10px !important;
        }
    }
}

// ============ start achievement-cast ============
.gray {
    color: $gray;
}

#achievement-cast {
    .search-month {
        margin: 1rem 0;
    }
    .table-list {
        margin-top: 1.5rem;
        th {
            p {
                font-size: 14px;
                font-weight: 600;
            }
        }
    }
}

@media screen and (max-width: 1200px) {
    .manage-cast {
        .form-inline {
            margin-top: 15px;
        }
    }
}

// ============ end achievement-cast ============
//  quick-view-tablet
.quick-view .manage-cast .list-cast {
    .hashtag div {
        font-size: 14px !important;
        padding: 3px !important;
        margin: 5px 3px !important;
    }
    .hashtag-1 {
        background-color: $white;
        border: 1px solid $red;
        color: $black;
    }
    .hashtag-2 {
        background-color: $white;
        border: 2px solid $green;
        color: $black;
    }
}

@media screen and (max-width: 1023px) {
    .quick-view .manage-cast .list-cast {
        .cast-item {
            width: 50%;
            padding: 0px 20px;
            .hashtag {
                height: 35px;
            }
        }
        .cast-item-1 {
            height: 500px;
            img {
                height: 400px
            }
        }
    }
}

@media screen and (min-width: 1023px) {
    .quick-view .manage-cast .list-cast {
        .cast-item {
            width: 33.33%;
            padding: 0px 20px;
            .hashtag {
                height: 35px;
            }
        }
        .cast-item-1 {
            height: 500px;
            img {
                height: 400px
            }
        }
    }
}
