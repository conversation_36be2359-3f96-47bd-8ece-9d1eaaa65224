@import "variables";
.manage-coupon {
    .box-coupon {
        border: none;
    }
    .table-cell {
        .alert-danger {
            white-space: normal;
        }
    }
    .box-coupon .table-left .table td,
    .box-coupon .table-left .table th {
        padding: 13px 1rem;
    }
    .form-control {
        display: inline-block;
        height: 30px;
        color: $black;
    }
    .table-left {
        .form-control {
            width: 90%;
        }
        .check-box {
            display: flex;
            .form-control {
                width: 50%;
            }
            .checkbox {
                display: flex;
                align-items: center;
                align-content: center;
                #check-box {
                    height: 20px;
                    width: 20px;
                }
                label {
                    margin: 0;
                }
            }
        }
        .table-list-coupon {
            width: 100%;
            border: 1px solid black;
            tr, th {
                border: none !important;
                font-weight: 400 !important;
                text-align: left !important;
            }
            .title-text {
                padding: 10px 0;
                border-bottom: 1px black dashed !important;
            }
            .list-coupon {
                margin-top: 10px;
                min-height: 300px;
                height: auto;
                display: flex;
                flex-wrap: wrap;
                flex-direction: row;
                .share-coupon {
                    display: flex;
                    margin-right: 40px;
                    margin-bottom: 15px;
                    align-items: center;
                    input {
                        width: 16px;
                        height: 16px;
                    }
                }
            }
        }
    }
    .check-radio1 {
        display: flex;
        align-items: center;
        align-content: center;
        label {
            margin: 0px 50px 0px 5px;
        }
        input {
            height: 20px;
            width: 20px;
        }
    }
    .time-header {
        color: black;
        display: flex;
        justify-content: space-between;
        margin: 25px 0px 10px 0px;
        .time-update {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .time-create {
            display: flex;
            align-items: center;
        }
    }
    .select-time {
        display: flex;
        justify-content: space-around;
        .check-radio2 {
            max-width: 16.3%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            .check {
                display: flex;
                align-items: center;
                align-content: center;
                input {
                    margin-right: 5px;
                    height: 16px;
                    width: 16px;
                }
                label {
                    margin: 0;
                }
            }
        }
        .col-sm-5,
        .col-sm-2,
        .col-sm-1 {
            padding: 0;
        }
        .width-1 {
            max-width: 0.3%;
            margin-top: 32px;
        }
        .text-head {
            margin-bottom: 10px;
        }
        .date {
            flex-direction: column;
        }
    }
    .date {
        .input-time,
        .input-time-2 {
            .year {
                max-width: 35%;
            }
            .month,
            .day {
                max-width: 20%;
            }
        }
    }
    .table-cell {
        .price {
            width: 80%;
        }
    }
    .table-date {
        tbody {
            width: 100%;
        }
    }
    .select-input,
    .select-input-2 {
        #hour-button,
        #minute-button,
        #hour-2-button,
        #minute-2-button {
            width: 60px;
            text-align: center;
            background-color: rgb(255, 255, 255);
            border-radius: 0px;
            padding: 5px 0px;
            z-index: 99999999;
            .ui-icon {
                width: 20px;
                height: 15px;
            }
        }
    }
    .button-submit {
        background-color: $btnSave;
        border: 2px solid $btnSave;
        margin: 10px 0px 0px 10px;
        width: 230px;
        padding: 10px 0px 10px 0px;
        border-radius: 20px;
        color: $white;
    }
    .alert-danger {
        padding: 0;
        color: red;
        background-color: white;
        border: none;
        margin: 0;
        font-size: 12px;
    }
    .overlay {
        position: fixed;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        display: none;
        background-color: #000;
        opacity: 0.5;
    }
    .popup {
        display: none;
        position: fixed;
        left: 50%;
        top: 20%;
        width: 400px;
        height: 200px;
        transform: translate(-50%, -50%);
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
        .content-popup {
            height: 90px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .clearfix {
            margin-top: 25px;
            display: flex;
            justify-content: flex-end;
            button {
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                border: none;
                cursor: pointer;
                opacity: 0.9;
                text-align: right;
                border-radius: 5px;
                margin: 0px 5px 0px 5px;
                padding: 10px 15px 10px 15px;
            }
            button:hover {
                opacity: 1;
            }
            .cancelbtn,
            .deletebtn {
                float: left;
                height: 40px;
            }
            .cancelbtn {
                background-color: #ccc;
                color: black;
            }
            .deletebtn {
                background-color: #f44336;
            }
            .submitbtn {
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                border: none;
                cursor: pointer;
                opacity: 0.9;
                text-align: right;
                border-radius: 5px;
                margin: 0px 5px 0px 5px;
                padding: 10px 15px 10px 15px;
                background-color: #0088ff;
            }
            .submitbtn:hover {
                opacity: 1;
            }
        }
        .container {
            padding: 16px;
            text-align: center;
        }
        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }
    }
    .ui-icon-triangle-1-s {
        width: 7px;
        height: 7px;
        box-sizing: border-box;
        position: absolute;
        left: 80%;
        top: 33%;
        transform: rotate(135deg);
        &::before {
            content: '';
            width: 100%;
            height: 100%;
            border-width: 2px 2px 0 0;
            border-style: solid;
            border-color: black;
            transition: .2s ease;
            display: block;
            transform-origin: 100% 0;
        }
        &:after {
            content: '';
            float: left;
            position: relative;
            top: -100%;
            width: 100%;
            height: 100%;
            border-width: 0 2px 0 0;
            border-style: solid;
            border-color: black;
            transform-origin: 100% 0;
            transition: .2s ease;
        }
    }
}
