function datepicker(inputName, formName) {
    $('.datepicker').daterangepicker({
        singleDatePicker: true,
        showDropdowns: false,
        minYear: 1901,
        maxYear: parseInt(moment().format('YYYY'), 10),
        locale: {
            format: 'YYYY-MM-DD',
            monthNames: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
            daysOfWeek: ["日", "月", "火", "水", "木", "金", "土"],
            cancelLabel: 'キャンセル',
            applyLabel: 'OK'
        },
        startDate: moment(dateNowPicker),
    }, function(start) {
        $(`input[name="${inputName}"]`).val(start.format('YYYY-MM-DD'));
        $(`${formName}`).submit()
    });
}

window.datepicker = datepicker;
