require('./payment');

if (PERMISSION) {
    $('.select-cast-page').find('.btn').attr('disabled', 'disabled');
    $('#form-cast').find('input').attr('disabled', 'disabled');
    $('.select-cast-page').find('#form-cast').removeAttr('action');
    $('.select-cast-page').find('#form-cast').submit(false);
    $('.select-cast-page').find('.cast-item-1').css('pointer-events', 'none');
    $('.select-cast-page').find('.calendar-cast-1').css('pointer-events', 'none');
}
