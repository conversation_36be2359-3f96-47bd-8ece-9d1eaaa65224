const imgUpload = require('./image_upload');

$(function() {
    imgUpload($('#add-manage-cast'));

    if (!$("#add-manage-cast .image-cast img").attr('src')) {
        $("#add-manage-cast .image-cast img").addClass('hidden');
    }

    for (let index = 0; index < $("#add-manage-cast .link-img").length; index++) {
        deleteUpload = $("#add-manage-cast .link-img")[index].nextElementSibling;
        if ($("#add-manage-cast .link-img")[index].value) {
            deleteUpload.classList.remove('hidden');
        } else {
            deleteUpload.classList.add('hidden');
        }
    }

    $("#add-manage-cast .form-upload div").click(function() {
        let parent = $(this).closest('.form-upload');
        let linkimg = parent.find('.link-img');
        let img = parent.find('img');
        let imgMain = $("#add-manage-cast .cast-item .img-cast");
        linkimg.val('');
        img.attr('src', '');
        img.addClass('hidden');
        if (linkimg.attr('name') == 'image[0]') {
            imgMain.attr('src', '');
            imgMain.addClass('hidden');
        }
        $(this).addClass('hidden');
    });
})

$(function() {
    $('#add-manage-cast .btn-submit').click(function() {
        showPopupOk('', 'この内容で登録してもよろしいでしょうか。', 'OK', function() {
            $('#form-add-cast').submit();
        })
    });
    var date = new Date();
    strTextDate(date, '#add-manage-cast .date-create');

    if (PERMISSION) {
        $('#add-manage-cast').find('a').removeAttr('href');
        $('#add-manage-cast').find('button').attr('disabled', 'disabled');
        $('#add-manage-cast').find('select').attr('disabled', 'disabled');
        $('#add-manage-cast').find('input').attr('disabled', 'disabled');
        $('#add-manage-cast').find('textarea').attr('disabled', 'disabled');
        $('#add-manage-cast').find('form').removeAttr('action');
        $('#add-manage-cast').find('form').submit(false);
    }
});
