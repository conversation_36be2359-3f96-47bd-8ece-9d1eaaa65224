@extends("admin.layouts.app")

@section('title', 'オプション編集')

@section('css')
    <link rel="stylesheet" href="{{ asset(mix('css/manage.option.css')) }}" type="text/css">
@endsection

@section('adminPageName')
    <h2>オプション登録</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.home.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">オプション編集</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="manage_option" id="manage-option">
        <div class="container-fluid">
            <div class="btn-header">
                <div class="row">
                    <div class="col-md-12">
                        <div class="group-button-top">
                            <div class="btn-group">
                                <a href="{{ route('admin.option.index') }}" class="btn btn-ct-primary"
                                    role="button">オプション管理トップ</a>
                                <a href="{{ route('admin.option.create') }}" class="btn btn-ct-primary"
                                    role="button">オプション登録</a>
                                <a href="#" class="btn btn-ct-primary active-primary" role="button">オプション編集</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="time-header">
                <div class="time-update">
                    <div>最終更新日</div>
                    <div>{{ formatDateJp($option_info->updated_at) }}</div>
                </div>
                <div class="time-create">
                    登録日 {{ formatDateJp($option_info->created_at) }}
                </div>
            </div>
            <div class="note2">
                ※編集完了後，既に，売上管理に表記されているこのオプションの料金は，凍結されます。
            </div>
            <div class="id">
                ID: {{ $option_info->id }}
            </div>
            <form method="POST" action="{{ route('admin.option.update', $option_info->id) }}" id="myFormSave">
                @csrf
                <input type="hidden" name="_method" value="PUT">
                <input type="hidden" name="option_info_id" value="{{ $option_info->id }}">
                <input type="hidden" name="quantityoption" value="{{ $quantityoption }}">
                <div class="head-radio">
                    @if (($option_info->tax_included == 0 && old('tax_included') == null) || (old('tax_included') == 0 && old('tax_included') != null))
                        <div class="col-md-6 radio">
                            <div class="tax_included">
                                <input type="radio" name="tax_included" value="1" checked>
                                <label for="tax_included">税込表示</label>
                            </div>
                            <div class="tax_included">
                                <input type="radio" name="tax_included" value="0" checked>
                                <label for="tax_included">税抜表示</label>
                            </div>
                        </div>
                    @else
                        <div class="col-md-6 radio">
                            <div class="tax_included">
                                <input type="radio" name="tax_included" value="1" checked>
                                <label for="tax_included">税込表示</label>
                            </div>
                            <div class="tax_included">
                                <input type="radio" name="tax_included" value="0">
                                <label for="tax_included">税抜表示</label>
                            </div>
                        </div>
                    @endif
                </div>
                <div class="input">
                    <div class="option-main">
                        <input type="hidden" name="idOptions[0]" value="{{ $options[0]->id }}">
                        <div class="name col-sm-6">
                            <label for="name">オプション名&emsp;&emsp;</label>
                            <input type="text" name="name_options[0]" class="form-control" maxlength="100"
                                value="{{ old('name_options.0') ? old('name_options.0') : $options[0]->name }}"
                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                            <div class=" alert alert-danger">{{ $errors->first('name_options.0') }}
                            </div>
                        </div>
                        <div class="price col-sm-6">
                            <label for="price">基本料金&emsp;</label>
                            <input type="text" name="price_options[0]" class="form-control"
                                value="{{ old('price_options.0') ? old('price_options.0') : $options[0]->price }}"
                                data-type="currency" maxlength="9" min='0'
                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                            円
                            <div class=" alert alert-danger">
                            </div>
                        </div>
                    </div>
                    <div class="option-sub">
                        @for ($i = 1; $i <= $quantityoption - 1; $i++)
                            <div class="sub">
                                <input type="hidden" name="idOptions[{{ $i }}]"
                                    value="{{ $options[$i]->id }}">
                                <div class="name col-sm-6">
                                    <label for="name">
                                        <div class="tamgiac"></div>
                                    </label>
                                    <input type="text" name="name_options[{{ $i }}]" class="form-control"
                                        value="{{ old("name_options.$i") ? old("name_options.$i") : $options[$i]->name }}"
                                        maxlength="100"
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                    <div class=" alert alert-danger">{{ $errors->first("name_options.$i") }}
                                    </div>
                                </div>
                                <div class="price col-sm-6">
                                    <label for="price">基本料金にプラス&emsp;</label>
                                    <input type="text" name="price_options[{{ $i }}]" class="form-control"
                                        value="{{ old("price_options.$i") ? old("price_options.$i") : $options[$i]->price }}"
                                        data-type="currency" maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                    円
                                    <div class=" alert alert-danger">
                                    </div>
                                </div>
                            </div>
                        @endfor
                        @for ($i = $quantityoption; $i <= 10; $i++)
                            <div class="sub">
                                <div class="name">
                                    <label for="name col-sm-6">
                                        <div class="tamgiac"></div>
                                    </label>
                                    <input type="text" name="name_options[{{ $i }}]" class="form-control"
                                        value="{{ old("name_options.$i") }}" maxlength="100"
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                    <div class=" alert alert-danger">{{ $errors->first("name_options.$i") }}
                                    </div>
                                </div>
                                <div class="price col-sm-6">
                                    <label for="price">基本料金にプラス&emsp;</label>
                                    <input type="text" name="price_options[{{ $i }}]" class="form-control"
                                        value="{{ old("price_options.$i") }}" data-type="currency" maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                    円
                                    <div class=" alert alert-danger">
                                    </div>
                                </div>
                            </div>
                        @endfor
                    </div>
                </div>
                <div class="price-cast">
                    <label for="price-cast">キャストへの支払い料金</label>&ensp;
                    <input type="text" name="price_cast" value="{{ $option_info->price_cast }}" data-type="currency"
                        maxlength="9" min='0' class="form-control"
                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                    円
                    <div class="alert alert-danger">{{ $errors->first('price_cast') }}</div>
                </div>
                <div class="checkbox">
                    <input type="checkbox" name="delete" class="checkbox-text" id="checkbox_delete">&ensp;削除
                </div>
                <div class="input-time">
                    <label for="time">公開日設定：</label>
                    @if ($date_start && old('year') == null)
                        @include('views.select_time', ['year' =>$date_start->year, 'month' =>$date_start->month, 'day'
                        =>$date_start->day])
                    @else
                        @include('views.select_time', [
                        'year' => old('year'),
                        'month' => old('month'),
                        'day' => old('day'),
                        ])
                    @endif
                    <div class="alert alert-danger">{{ $errors->first('day') }}</div>
                </div>
                <div class="btn-add">
                    <button class="btn btn-manage2 submit" type="button" id="myConfirm">編集完了</button>
                </div>
                <input name="popup2" value="{{ session('popup') ? '編集完了しました。' : '' }}" type="hidden">
            </form>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_option.js') }} "></script>
@endsection
