@extends('admin.layouts.app')

@inject('mRoom', 'App\Models\Room')
@inject('mRoomCalendar', 'App\Models\CastCalendar')

@section('title', '部屋割編集 ')

@section('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="{{ assetMix('css/admin.room.calendar.create.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2> 部屋割り管理 </h2>
@endsection
@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active"> 部屋割編集 </li>
            </ol>
        </nav>
    </div>
@endsection
@section('content')
    <div class="manage-room" id="manage-list-room">
        <div class="row">
            <div class="col-md-12">
                <div class="group-button-top">
                    <div class="btn-group">
                        <a href="{{ route('admin.calendar.index') }}" class="btn btn-ct-primary" role="button">部屋割管理トップ</a>
                        <a href="{{ route('admin.calendar.create') }}" class="btn btn-ct-primary" role="button">部屋割登録</a>
                        <a href="{{ route('admin.edit.all.room.calendar', $dateSelect->toDateString()) }}"
                            class="btn btn-ct-primary active-primary" role="button">部屋割編集</a>
                    </div>
                </div>
            </div>
            <div class="date" id="dateNow">

            </div>
        </div>
        <div class="row" id="show-table">
            <div class="name-table">
                すでに割り振られている情報を編集できる画面です。
            </div>
            <div class="row select-date">
                <div class="col-md-6 col-lg-6 calendar">
                    {{ formatDateJp($dateSelect) }} の部屋割り振り編集画面です。
                    <div class="border-orange"></div>
                    <div class="img-calendar">
                        <img src="{{ asset('images/icons/calender_clock.png') }}" alt="calender clock"
                            class="datepicker img-datepicker">
                        <input type="date" name="date" class="hidden" value="{{ $dateNow->format('Y-m-d') }}">
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 cast-calendar">
                    <button class="btn" id="showPopup" date-time="{{ $dateSelect->toDateString() }}">
                        {{ formatDateJp($dateSelect) }} のキャスト出勤情報を見る
                    </button>
                </div>
            </div>
            <div class="list-table-room">
                <form action="{{ route('admin.update.room.calendar', $dateSelect) }}" method="POST" id="form-edit-room">
                    @csrf
                    <input type="hidden" name="date_time" class="hidden"
                        value="{{ $dateSelect->toDateString() }}">
                    @foreach ($rooms as $index => $room)
                        <div class="table-responsive">
                            <table class="table table-hover table-shadown table-item" data-room-id="{{ $index }}">
                                <thead>
                                    <td width="5%">部屋廻し</td>
                                    <td width="5%">部屋番号</td>
                                    <td width="20%%">キャスト名</td>
                                    <td width="23.33%" class="text-center">使用時間１</td>
                                    <td width="23.33%" class="text-center">使用時間２</td>
                                    <td width="23.33%" class="text-center">使用時間３ {{ $mRoomCalendar::SHARE_ROOM }}</td>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td rowspan="3" class="checkbox">
                                            <input type="radio" name="share_room" value="{{ $room->id }}"
                                                data-room-id="{{ $room->id }}" class="check-share"
                                                @if ($isShare[$index] == $mRoomCalendar::SHARE_ROOM) checked @endif>
                                            <div class="mask"></div>
                                        </td>
                                        <td rowspan="3" class="name-room"> {{ $room->name }} 号室</td>
                                        <td class="name-cast">
                                            <select name="cast_id[{{ $index }}][1]" class="cast_name"
                                                data-slot="1">
                                                @if (isset($roomCalendars[$index][1]))
                                                    <option value="0"> </option>
                                                    <option value="{{ $roomCalendars[$index][1]->cast_id }}" selected>
                                                        {{ $roomCalendars[$index][1]->cast_name }}
                                                    </option>
                                                @else
                                                    <option value="0" selected> </option>
                                                @endif
                                            </select>
                                            <p class="error_name edit text-danger" data-slot="1"></p>
                                        </td>
                                        <td>
                                            <div class="hour" data-slot="1">
                                                @if (isset($roomCalendars[$index][1]))
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_start[{$index}][1]",
                                                    'minuteType' => "minute_start[{$index}][1]",
                                                    'hour' => substr($roomCalendars[$index][1]->date_start,11,2),
                                                    'minute' => substr($roomCalendars[$index][1]->date_start,14,2)
                                                    ])
                                                    <span> ~ </span>
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_end[{$index}][1]",
                                                    'minuteType' => "minute_end[{$index}][1]",
                                                    'hour' => substr($roomCalendars[$index][1]->date_end,11,2),
                                                    'minute' => substr($roomCalendars[$index][1]->date_end,14,2)
                                                    ])
                                                @else
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_start[{$index}][1]",
                                                    'minuteType' => "minute_start[{$index}][1]"
                                                    ])
                                                    <span> ~ </span>
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_end[{$index}][1]",
                                                    'minuteType' => "minute_end[{$index}][1]"
                                                    ])
                                                @endif
                                            </div>
                                            <div class="text-danger error mt-2"></div>
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="name-cast">
                                            <select name="cast_id[{{ $index }}][2]" class="cast_name"
                                                data-slot="2">
                                                @if (isset($roomCalendars[$index][2]))
                                                    <option value="0"> </option>
                                                    <option value="{{ $roomCalendars[$index][2]->cast_id }}" selected>
                                                        {{ $roomCalendars[$index][2]->cast_name }}
                                                    </option>
                                                @else
                                                    <option value="0" selected> </option>
                                                @endif
                                            </select>
                                            <p class="error_name edit text-danger" data-slot="2"></p>
                                        </td>
                                        <td>
                                        </td>
                                        <td>
                                            <div class="hour" data-slot="2">
                                                @if (isset($roomCalendars[$index][2]))
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_start[{$index}][2]",
                                                    'minuteType' => "minute_start[{$index}][2]",
                                                    'hour' => substr($roomCalendars[$index][2]->date_start,11,2),
                                                    'minute' => substr($roomCalendars[$index][2]->date_start,14,2)
                                                    ])
                                                    <span> ~ </span>
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_end[{$index}][2]",
                                                    'minuteType' => "minute_end[{$index}][2]",
                                                    'hour' => substr($roomCalendars[$index][2]->date_end,11,2),
                                                    'minute' => substr($roomCalendars[$index][2]->date_end,14,2)
                                                    ])
                                                @else
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_start[{$index}][2]",
                                                    'minuteType' => "minute_start[{$index}][2]"
                                                    ])
                                                    <span> ~ </span>
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_end[{$index}][2]",
                                                    'minuteType' => "minute_end[{$index}][2]"
                                                    ])
                                                @endif
                                            </div>
                                            <div class="text-danger error mt-2"></div>
                                        </td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="name-cast">
                                            <select name="cast_id[{{ $index }}][3]" class="cast_name"
                                                data-slot="3">
                                                @if (isset($roomCalendars[$index][3]))
                                                    <option value="0"> </option>
                                                    <option value="{{ $roomCalendars[$index][3]->cast_id }}" selected>
                                                        {{ $roomCalendars[$index][3]->cast_name }}
                                                    </option>
                                                @else
                                                    <option value="0" selected> </option>
                                                @endif
                                            </select>
                                            <p class="error_name edit text-danger" data-slot="3"></p>
                                        </td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <div class="hour" data-slot="3">
                                                @if (isset($roomCalendars[$index][3]))
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_start[{$index}][3]",
                                                    'minuteType' => "minute_start[{$index}][3]",
                                                    'hour' => substr($roomCalendars[$index][3]->date_start,11,2),
                                                    'minute' => substr($roomCalendars[$index][3]->date_start,14,2)
                                                    ])
                                                    <span> ~ </span>
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_end[{$index}][3]",
                                                    'minuteType' => "minute_end[{$index}][3]",
                                                    'hour' => substr($roomCalendars[$index][3]->date_end,11,2),
                                                    'minute' => substr($roomCalendars[$index][3]->date_end,14,2)
                                                    ])
                                                @else
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_start[{$index}][3]",
                                                    'minuteType' => "minute_start[{$index}][3]"
                                                    ])
                                                    <span> ~ </span>
                                                    @include('admin.room_calendar.select_time_hour', [
                                                    'hourType' => "hour_end[{$index}][3]",
                                                    'minuteType' => "minute_end[{$index}][3]"
                                                    ])
                                                @endif
                                            </div>
                                            <div class="text-danger error mt-2"></div>
                                        </td>

                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    @endforeach
                </form>
                <div class="form-group text-center">
                    <button type="submit" class="btn submit btn-save"> 編集完了 </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        var dateNowPicker = @json($dateSelect->toDateString());
        const SHARE_ROOM = @json($mRoomCalendar::SHARE_ROOM);
        const NO_SHARE_ROOM = @json($mRoomCalendar::NO_SHARE_ROOM);
        var idCasts = @json($idCasts);
        var roomShare = @json($roomShare);
    </script>
    <script src="{{ assetMix('js/page_admin_room_calendar_edit.js') }} "></script>
@endsection
