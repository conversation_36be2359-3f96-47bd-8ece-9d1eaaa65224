@php
use Carbon\Carbon;
$nowDate = Carbon::now();
@endphp
@inject('OrderModel', 'App\Models\Order')
@inject('CouponModel', 'App\Models\Coupon')
@inject('constantTimeTable', 'App\Constants\TimeTable')
@inject('constantCustomer', 'App\Constants\Customer')

@extends('admin.layouts.app')

@section('title', '予約登録可能画面')

@section('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="{{ assetMix('css/admin.timetable.top.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2>タイムテーブル</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.home.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">予約登録可能画面</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="manage-timetable-top view-top" id="manage-timetable-top">
        <div class="row">
            <div class="col-md-12">
                <div class="row-1">
                    <div class="date">
                        <div id="date-select"></div>
                        <div class="img-calendar">
                            <form action="{{ route('admin.timetable.index') }}" method="GET" id="search-cast-calendar">
                                <div class="date-choose">
                                    <img src="{{ asset('images/icons/calender_clock.png') }}" alt="calender clock"
                                        class="datepicker img-datepicker">
                                    <div class="text-date">{{ formatDateJp($dateSelect) }}</div>
                                    <input type="date" name="date" class="hidden"
                                        value="{{ $dateSelect->toDateString() }}">
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="btn-active">
                        @include('admin.timetable.breadcrumb')
                    </div>
                </div>
                <div class="row filter-time">
                    <div class="col-lg-2">
                        <a class="btn btn-primary reset" href="{{ route('admin.timetable.index', 'now=' . $nowDate) }}"
                            role="button">今の時間へ戻る</a>
                    </div>
                    <div class="col-md-5">
                        <div class="infor-book">
                            <span>予約件数: {{ $countOrderTotal }}</span>
                            <span class="text">入浴中: {{ $countOrderStart }}</span>
                            <span class="text">残:
                                {{ $countOrderTotal - $countOrderStart - $countOrderDone }}</span>
                            <span class="text">完了: {{ $countOrderDone }}</span>
                        </div>
                    </div>
                    <div class="col-lg-5 text-right">
                        <a href="{{ route('admin.export.data.booking', $dateSelect->toDateString()) }}"
                            class="btn button-export">CSV出力</a>
                    </div>
                </div>
                @include('admin.timetable.table_timetable',[
                'listDataCast' => $listDataCast,
                'hasTextCourse' => true,
                'addBooking' => $addBooking,
                'hasTextConfirmPhone' => false,
                'iconEdit' => true
                ])
            </div>
        </div>
        <input name="popup" value="{{ session('msg') ? session('msg') : '' }}" type="hidden">
        <div class="modal popup-booking" id="myModalBooking0">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    @include('admin.timetable.popup_booking', [
                    'type' => 0,
                    'listCourseActive' => $listCourse,
                    ])
                </div>
            </div>
        </div>
        <div class="modal popup-booking" id="myModalBooking1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    @include('admin.timetable.popup_booking', [
                    'type' => 1,
                    'listCourseActive' => $listCourse,
                    ])
                </div>
            </div>
        </div>
        <div class="modal popup-booking" id="myModalBooking2">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    @include('admin.timetable.popup_booking', [
                    'type' => 2,
                    'listCourseActive' => $listCourse,
                    ])
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        var dateNowPicker = @json($dateSelect->toDateString());
        const CLOSE_HOURS = @json(CLOSE_HOURS);
        const BUSINESS_HOURS = @json(BUSINESS_HOURS);
        var NOT_IS_MEMBER = @json($constantCustomer::NOT_IS_MEMBER);
        var IS_MEMBER = @json($constantCustomer::IS_MEMBER);
        var IS_CHOOSE_COUPON_MEMBER = @json($constantCustomer::IS_CHOOSE_COUPON_MEMBER);
    </script>
    <script src="{{ assetMix('js/page_admin_timetable.js') }} "></script>
@endsection
