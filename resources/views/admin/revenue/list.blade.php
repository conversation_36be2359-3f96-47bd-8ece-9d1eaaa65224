@inject('modelOrder', 'App\Models\Order')
@inject('modelCourse', 'App\Models\Course')
@inject('mCast', 'App\Models\Cast' )
@inject('orderService', 'App\Services\OrderService')
@extends('admin.layouts.app')

@php
use Carbon\Carbon;
use App\Constants\Orders;
$now = Carbon::now();
if ($now->hour < CLOSE_HOURS - 24) {
    $now->subDay();
}
@endphp

@section('title', '売上管理トップ')

@section('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="{{ assetMix('css/admin.revenue.css') }}" type="text/css">

@endsection

@section('adminPageName')
    <h2 class="page-name-text">売上管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">売上管理トップ</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="page-revenue" id="manage-revenue-list">
        @include('admin.revenue.group_button_top')
        <div class="row">
            <div class="col-lg-12">
                <div class="date row-date-top">
                    <form action="" id="form">
                        <div class="form-row align-items-center">
                            {{ formatDateJp($date) }} <span>{{ strJPDaysOfWeek($date) }}</span>
                            <div class="img-datepicker">
                                <img src="{{ asset('images/icons/calender_clock.png') }}" alt="calender clock"
                                    class="datepicker">
                                <input type="date" name="date" class="hidden"
                                    value="{{ $date->format('Y-m-d') }}">
                            </div>
                        </div>
                    </form>
                </div>
                @php
                    $totalPriceAllOrder = 0;
                    $totalPriceAllOrderForCast = 0;
                    $priceOrdersByCash = 0;
                    $dataPriceOrders=[];
                @endphp
                @foreach ($dataOrders as $index => $order)
                    @php

                        $totalOrderPrice = calculateOrderTotalPrice($order);
                        $dataPriceOrders[$index] = $totalOrderPrice;
                        if ($order->type_pay ==1){
                            $priceOrdersByCash += $totalOrderPrice;
                        }

                        // total price from order for shop
                        $totalPriceAllOrder += $totalOrderPrice - $order->price_cast;
                        // total price shop pay for cast
                        $totalPriceAllOrderForCast += $order->price_cast;
                    @endphp
                @endforeach
                <div class="revenue-info">
                    <div class="row-revenue">
                        <div class="col-sm-4">
                            <div class="box">
                                <div class="box-price">
                                    <div class="text">現在の現金</div>
                                    <div class="price">
                                        {{ number_format($priceOrdersByCash - $totalStatistic['priceOrderForCast'] - $totalStatistic['costShop']) }}
                                    </div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">現金売上</div>
                                    <div class="min-text">（クレカ手数料を省いた決済を除く）</div>
                                    <div class="price">
                                        {{ number_format($priceOrdersByCash - $totalStatistic['priceOrderForCast']) }}
                                    </div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">経費</div>
                                    <div class="price">{{ number_format($totalStatistic['costShop']) }}</div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">キャストへの支払い</div>
                                    <div class="price">
                                        {{ number_format($totalStatistic['priceForCast']) }}</div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">手当</div>
                                    <div class="price">
                                        {{ number_format($totalStatistic['priceSupportByCash']) }}</div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                        </div>
                        <div class="col-sm-4 row-center">
                            <div class="box">
                                <div class="box-price">
                                    <div class="text">クレジットカード</div>
                                    <div class="price">{{ number_format($totalStatistic['priceOrdersByCard']) }}
                                    </div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">カード売上</div>
                                    <div class="price">{{ number_format($totalStatistic['taxPriceOrdersByCard']) }}</div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">内手数料</div>
                                    <div class="price">{{ number_format($totalStatistic['priceOrdersByCard'] - $totalStatistic['taxPriceOrdersByCard']) }}</div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">件数</div>
                                    <div class="price">{{ $totalStatistic['numberOrdersByCard'] }}</div>
                                </div>
                                <div class="unit">件</div>
                            </div>
                            @php
                                $dateCard = clone $date;
                                if ($dateCard->day < 15) {
                                    $dateCard->day(15);
                                } else {
                                    $dateCard->endOfMonth();
                                }
                            @endphp
                            <div class="line">
                                <div class="text">入金予定日</div>
                                <div class="date">{{ formatDateJp($dateCard) }}</div>
                            </div>
                        </div>
                        <div class="col-sm-4 row-right">
                            <div class="box">
                                <div class="box-price">
                                    <div class="text">現在の経常利益</div>
                                    <div class="price">
                                        {{ number_format($totalStatistic['dormitoryPrice'] +$totalStatistic['expenses'] +$totalStatistic['priceLiving'] +$totalStatistic['priceDecorateRoom']) }}
                                    </div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">寮費</div>
                                    <div class="price">
                                        {{ number_format($totalStatistic['dormitoryPrice']) }}</div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">小口</div>
                                    <div class="price">{{ number_format($totalStatistic['priceLiving']) }}
                                    </div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">雑費</div>
                                    <div class="price">
                                        {{ number_format($totalStatistic['expenses']) }}</div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                            <div class="line">
                                <div class="line-price">
                                    <div class="text">美装代</div>
                                    <div class="price">{{ number_format($totalStatistic['priceDecorateRoom']) }}
                                    </div>
                                </div>
                                <div class="unit">円</div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr class="dashed">
                <div class="daily-cost">
                    <h4 class="title-table-info-orders">
                        <div class="square"></div> 経費入力欄
                    </h4>
                    <form action="{{ route('admin.revenue.cost.shop') }}" method="POST" id="form-cost-shop">
                        @csrf
                        <input type="hidden" name="date" value="{{ $date->format('Y-m-d') }}">
                        <div class="form-input-cost">
                            <div class="label">
                                <div class="col-sm-2"></div>
                                <div class="col-sm-5">項目</div>
                                <div class="col-sm-5">代金</div>
                            </div>
                            <div class="list-input">
                                @foreach ($listCostShop as $item)
                                    <div class="line-input" id="old-input-{{ $item->id }}">
                                        <div class="col-sm-2">
                                            <button type="button" class="btn btn-del-input">削除</button>
                                            <div class="alert alert-danger"></div>
                                        </div>
                                        <div class="col-sm-5">
                                            <input type="text" class="form-control" name="name[{{ $item->id }}]"
                                                value="{{ $item->name }}" maxlength="255"
                                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                            <div class="alert alert-danger"></div>
                                        </div>
                                        <div class="col-sm-5">
                                            <input class="form-control" type="text" name="price[{{ $item->id }}]"
                                                min='0' value="{{ $item->price }}" data-type="currency" maxlength="9"
                                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                            <div class="alert alert-danger"></div>
                                        </div>
                                    </div>
                                @endforeach
                                <div class="line-input none" id="line-input-0">
                                    <div class="col-sm-2">
                                        <button type="button" class="btn btn-del-input">削除</button>
                                        <div class="alert alert-danger"></div>
                                    </div>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control" name="name_item[]" value=""
                                            maxlength="255"
                                            oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                        <div class="alert alert-danger"></div>
                                    </div>
                                    <div class="col-sm-5">
                                        <input class="form-control" type="text" name="price_item[]" min='0' value=""
                                            data-type="currency" maxlength="9"
                                            oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                        <div class="alert alert-danger"></div>
                                    </div>
                                </div>
                                @for ($i = 1; $i <= 4 - count($listCostShop); $i++)
                                    <div class="line-input" id="line-input-{{ $i }}">
                                        <div class="col-sm-2">
                                            <button type="button" class="btn btn-del-input">削除</button>
                                            <div class="alert alert-danger"></div>
                                        </div>
                                        <div class="col-sm-5">
                                            <input type="text" class="form-control" name="name_item[]" value=""
                                                maxlength="255"
                                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                            <div class="alert alert-danger"></div>
                                        </div>
                                        <div class="col-sm-5">
                                            <input class="form-control" type="text" name="price_item[]" min='0' value=""
                                                data-type="currency" maxlength="9"
                                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                            <div class="alert alert-danger"></div>
                                        </div>
                                    </div>
                                @endfor
                            </div>
                            <div class="btn-add-input">
                                <div class="col-sm-2"></div>
                                <div class="col-sm-10">
                                    <div>項目追加</div>
                                    <button type="button" class="btn-add">+</button>
                                </div>
                            </div>
                            <div class="btn-submit">
                                <div class="col-sm-2"></div>
                                <div class="col-sm-10">
                                    <button type="button" class="btn btn-save">保存</button>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="alert-errors" value="{{ session('alert-errors') }}">
                    </form>
                </div>
                <hr class="dashed">

                <h4 class="title-table-info-orders text-center">現在の状態</h4>
                <div class="revenue-order">
                    <div class="count-book">
                        <p class="text">発進前</p>
                        <p class="number no-happened">{{ $arrCountBookByStatus[0] }}</p>
                        <p class="text">通常</p>
                        <p class="number happenning">{{ $arrCountBookByStatus[1] }}</p>
                        <p class="text">延長</p>
                        <p class="number extension">{{ $arrCountBookByStatus[2] }}</p>
                        <p class="text">完了</p>
                        <p class="number finshed">{{ $arrCountBookByStatus[3] }}</p>
                        <p class="text">合計</p>
                        <p class="number total">{{ $arrCountBookByStatus[0] + $arrCountBookByStatus[1] + $arrCountBookByStatus[2] + $arrCountBookByStatus[3] }}</p>
                    </div>
                    <div class="count-book">
                        <p class="text price-red">店売上</p>
                        <p class="number price-red">{{ number_format($totalPriceAllOrder) . '  円' }}</p>
                        <p class="text price-blue">キャストへの支払い額</p>
                        <p class="number price-blue">{{ number_format($totalPriceAllOrderForCast) . '  円' }}</p>
                    </div>
                    <a href="{{ route('admin.export.data.order', $date->toDateString()) }}"
                        class="btn button-export">CSV出力</a>
                </div>
                <div class="table-info-orders table-responsive">
                    <table class="table table-hover table-bordered table-shadown text-center">
                        <thead>
                            <tr>
                                <th>編集</th>
                                <th class="stt">お客様 <br> 番号</th>
                                <th>状態</th>
                                <th colspan="2">キャスト名</th>
                                <th>部屋番号</th>
                                <th colspan="2">コース</th>
                                <th>種別</th>
                                <th>
                                    モニター <br> チェック
                                </th>
                                <th>入浴前 <br> アンケート</th>
                                <th>入浴後 <br> アンケート</th>
                                <th class="time">入り時間</th>
                                <th class="time">上がり時間</th>
                                <th colspan="2" class="name">お客様名</th>
                                <th colspan="2">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (!$dataOrders)
                                <p class="text-center">※該当なデータがありません</p>
                            @else
                                @php
                                    $castIds = array_values(array_unique($dataOrders->pluck('cast_id')->all()));
                                @endphp
                                @foreach ($dataOrders as $index => $order)
                                    <tr
                                        class="tr-order status-normal" data-order-status="{{$order->status}}"
                                        data-order-is_ten_minutes_before="{{$order->is_ten_minutes_before}}"
                                        data-order-can_edit="{{$date->format('Y-m-d') == $now->format('Y-m-d')}}">
                                        <td rowspan="3">
                                            @php
                                                $yesterday = clone $now;
                                                $yesterday = $yesterday->subDay();
                                                $canEdit = $date->format('Y-m-d') == $now->format('Y-m-d') || $date->format('Y-m-d') == $yesterday->format('Y-m-d');
                                            @endphp
                                            <div>
                                                @if ($canEdit)
                                                    <a href="{{ route('admin.manage.status.edit', $order->id) }}" role="button"
                                                        class="edit"> <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                                    </a>
                                                @else
                                                    <div class="edit"><i class="fa fa-pencil-square-o" aria-hidden="true"></i></div>
                                                @endif
                                            </div>
                                        </td>
                                        <td rowspan="3" class="text-white">{{ $order->number }}</td>
                                        <td class="status text-white">
                                            @if ($order->status == $modelOrder::STATUS_NO_HAPPEN_YET)
                                                発進前
                                            @elseif($order->status == $modelOrder::STATUS_HAPPENING)
                                                通常
                                            @elseif($order->status == $modelOrder::STATUS_EXTENSION)
                                                延長
                                            @elseif($order->status == $modelOrder::STATUS_FINISH)
                                                完了
                                            @endif
                                        </td>
                                        <td colspan="2" class="text-white">{{ $order->casts->name_cast }}</td>
                                        <td class="text-white">
                                            @if (isset($castRoomCalendars[$order->cast_id]))
                                                {{ $castRoomCalendars[$order->cast_id]['room_name'] }}
                                            @endif
                                        </td>
                                        <td colspan="2" class="text-white">
                                            @if (isset($modelCourse::withTrashed()->find($order->course_id)->name))
                                                {{ $modelCourse::withTrashed()->find($order->course_id)->name }}
                                            @else ※データ無し
                                            @endif
                                        </td>
                                        <td class="text-white">
                                            @if ($order->type_fee == $modelOrder::TYPE_APPOINT)
                                                指名
                                            @elseif ($order->type_fee == $modelOrder::TYPE_NET)
                                                ネット
                                            @elseif($order->type_fee == $modelOrder::TYPE_PANEL) パネル
                                            @else フリー
                                            @endif
                                        </td>
                                        <td class="text-white">
                                            @if ($order->casts->monitor_check == $mCast::CHECK_MONITOR)
                                                有り
                                            @else 無し
                                            @endif
                                        </td>
                                        <td class="text-white">
                                            @if ($order->is_first == $modelOrder::FIRST_TIME)
                                                要
                                            @else 不要
                                            @endif
                                        </td>
                                        <td class="text-white">
                                            @if ($order->is_first == $modelOrder::FIRST_TIME)
                                                要
                                            @else 不要
                                            @endif
                                        </td>
                                        @php
                                            $timeCourse = $modelCourse::withTrashed()->find($order->course_id)->time ?? 0;
                                        @endphp
                                        <td class="time-start text-white">
                                            <p class="time-start-plan text-white">
                                                @php
                                                    $dateStartString = $order->actual_date_start ? $order->actual_date_start : $order->date_start;
                                                    $dateStart = Carbon::parse($dateStartString);
                                                    $hourStart = $dateStart->hour;
                                                    if ($dateStart->format('d') != Carbon::parse($order->date)->format('d')) {
                                                        $hourStart = $dateStart->hour + 24;
                                                    }
                                                @endphp
                                                {{ sprintf('%02d', $hourStart) . ':' . sprintf('%02d', $dateStart->minute) }}
                                            </p>
                                        </td>
                                        <td class="time-end">
                                            <p class="time-end-plan text-white">
                                                @php
                                                    $timeStart = Carbon::parse($order->date_start);
                                                    $timeEnd = Carbon::parse($order->date_end);
                                                    // if ($order->actual_date_start) {
                                                    //     if (!$order->actual_date_end) {
                                                    //         $actualStart = Carbon::parse($order->actual_date_start);
                                                    //         $timeEnd = $actualStart->addMinutes($timeEnd->diffInMinutes($timeStart));
                                                    //     }
                                                    //     $timeStart = Carbon::parse($order->actual_date_start);
                                                    // }
                                                    if ($order->actual_date_end) {
                                                        $timeEnd = Carbon::parse($order->actual_date_end);
                                                    }
                                                    $hourEnd = $timeEnd->hour;
                                                    if ($timeEnd->day != Carbon::parse($order->date)->day) {
                                                        $hourEnd = $timeEnd->hour + 24;
                                                    }
                                                @endphp
                                                {{ sprintf('%02d', $hourEnd) . ':' . sprintf('%02d', $timeEnd->minute) }}
                                            </p>
                                            <p class="time-end-actual text-white">
                                                @if ($order->minute_extension && $order->status != $modelOrder::STATUS_FINISH)
                                                    @php
                                                        $dateEndActual = clone $timeEnd;
                                                        $dateEndActual->addMinutes($order->minute_extension);
                                                        $hourEndActual = $dateEndActual->hour;
                                                        if ($dateEndActual->day != Carbon::parse($order->date)->day) {
                                                            $hourEndActual = $dateEndActual->hour + 24;
                                                        }
                                                    @endphp
                                                    ({{ sprintf('%02d', $hourEndActual) . ':' . sprintf('%02d', $dateEndActual->minute) }})
                                                @endif
                                            </p>
                                        </td>
                                        <td colspan="2" class="text-white">
                                            {{ $order->name_customer }}
                                        </td>
                                        <td colspan="2">
                                            <div class="action" order-id={{ $order->id }}
                                                cast-id={{ $order->cast_id }} status={{ $order->status }}>
                                                <button class="btn btn-table btn-back-start"
                                                    role="button" status-id="{{ $modelOrder::STATUS_NO_HAPPEN_YET }}"><i
                                                        class="fa fa-undo" aria-hidden="true"></i></button>
                                                <button
                                                    class="btn btn-table btn-start" role="button"
                                                    status-id="{{ $modelOrder::STATUS_HAPPENING }}">発進</button>
                                                <button
                                                    class="btn btn-table btn-back-finsh"
                                                    role="button"
                                                    status-id="{{ $order->minute_extension ? $modelOrder::STATUS_EXTENSION : $modelOrder::STATUS_HAPPENING }}"><i
                                                        class="fa fa-undo" aria-hidden="true"></i></button>
                                                <button
                                                    class="btn btn-table btn-finsh" role="button"
                                                    status-id="{{ $modelOrder::STATUS_FINISH }}">完了</button>
                                                <button
                                                    class="btn btn-table btn-back-ten-minute-before"
                                                    role="button" style="width: 75px;"
                                                    status-id="{{ $order->minute_extension ? $modelOrder::STATUS_EXTENSION : $modelOrder::STATUS_HAPPENING }}"><i
                                                        class="fa fa-undo" aria-hidden="true"></i>&nbsp;10 分前</button>
                                                <button
                                                    class="btn btn-table btn-ten-minute-before" role="button"
                                                    status-id="{{ $order->minute_extension ? $modelOrder::STATUS_EXTENSION : $modelOrder::STATUS_HAPPENING }}">10 分前</button>
                                                <button class="btn btn-table btn-extension" role="button"
                                                    status-id="{{ $modelOrder::STATUS_EXTENSION }}">延長</button>
                                                <button
                                                    class="btn btn-table btn-back-extension"
                                                    role="button"
                                                    data-count={{ $order->minute_extension ? $order->minute_extension / $minuteExtension : 0 }}
                                                    status-id="{{ $modelOrder::STATUS_HAPPENING }}"><i class="fa fa-undo"
                                                        aria-hidden="true"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr rowspan="1" colspan="12" class="mini-table">
                                        <th class="font-weight-normal">予約時金額</th>
                                        <th class="font-weight-normal">指名料金</th>
                                        <th class="font-weight-normal">予約時合計金額</th>
                                        <th class="font-weight-normal">割引名目 1</th>
                                        <th class="font-weight-normal">割引名目 2</th>
                                        <th class="font-weight-normal">割引名目 3</th>
                                        <th class="font-weight-normal">割引額 1</th>
                                        <th class="font-weight-normal">割引額 2</th>
                                        <th class="font-weight-normal">割引額 3</th>
                                        <th class="font-weight-normal">割引額合計</th>
                                        <th class="font-weight-normal">スタートプラス</th>
                                        <th class="font-weight-normal">オプション</th>
                                        <th class="font-weight-normal">支払い方法</th>
                                        <th class="font-weight-normal">ユーザー支払い金額</th>
                                        <th class="font-weight-normal">キャストへの支払い額</th>
                                        <th class="font-weight-normal">店売上</th>
                                    </tr>
                                    @php
                                        $totalOrderPriceShow = calculateCustomerPayOrderTotalPrice($order);
                                    @endphp
                                    <tr rowspan="1" colspan="12" class="mini-table">
                                        <th class="font-weight-normal">
                                            {{ number_format($order->course_price) }} 円
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ number_format($order->price_fee_new) }} 円
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ number_format($order->course_price + $order->price_fee_new) }} 円
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ $order->coupon_1_name ? $order->coupon_1_name : '-' }}
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ $order->coupon_2_name ? $order->coupon_2_name : '-' }}
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ $order->coupon_3_name ? $order->coupon_3_name : '-' }}
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ $order->coupon_1_price ? number_format($order->coupon_1_price) . '  円' : '-' }}
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ $order->coupon_2_price ? number_format($order->coupon_2_price) . '  円' : '-' }}
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ $order->coupon_3_price ? number_format($order->coupon_3_price) . '  円' : '-' }}
                                        </th>
                                        <th class="font-weight-normal">
                                            {{ number_format($order->coupon_1_price + $order->coupon_2_price + $order->coupon_3_price) }} 円
                                        </th>
                                        <th class="font-weight-normal">
                                            @if ($order->is_minute_add)
                                                有り
                                            @else 無し
                                            @endif
                                        </th>
                                        <th class="font-weight-normal">
                                            @if ($order->option_parent_id)
                                                {{ $order->option_parent_name }} {{ $order->option_sub_name ? '('.$order->option_sub_name.')' : '' }}
                                            @else 無し
                                            @endif
                                        </th>
                                        <th class="font-weight-normal">
                                            @if ($order->type_pay == Orders::TYPE_PAYMENT_CASH)
                                            現金
                                            @else カード
                                            @endif
                                        </th>
                                        <th class="font-weight-normal">
                                            <span id="total_order_price_{{ $order->id }}" data-price="{{ $totalOrderPriceShow }}">{{ number_format($totalOrderPriceShow) }} 円</span>
                                        </th>
                                        <th class="font-weight-normal">
                                            <span id="cast_price_{{ $order->id }}" data-price="{{ $order->price_cast }}">{{ number_format($order->price_cast) }} 円</span>
                                        </th>
                                        <th class="font-weight-normal">
                                            <span id="price_for_shop_{{ $order->id }}">{{ number_format($dataPriceOrders[$index] - $order->price_cast) }} 円</span>
                                        </th>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                </div>
                <hr class="dashed">
                <div class="revenue-number">
                    <p>現在の来店顧客数 <span>{{ number_format($totalCustomer) }} 件</span></p>
                    <p>現在のキャスト出勤数 <span>{{ isset($totalCastWorking) ? $totalCastWorking : 0 }} 名</span></p>
                    <p>現在の売上金額 <span>{{ number_format($totalStatistic['totalRevenue'] + $priceOrdersByCash) }} 円</span></p>
                </div>
                <hr class="line">
                <div class="table-info-course-number table-responsive">
                    <table class="table table-bordered table-shadown text-center">
                        <tbody>
                            <tr>
                                <td>コース名</td>
                                @php
                                    $countCourse = 0;
                                    $countCourseRevenue = 0;
                                @endphp
                                @forelse ($totalCourseCate as $item)
                                    <td>{{ $item->name }}</td>
                                @empty
                                @endforelse
                                <td>合計</td>
                                <td>売上見込み</td>
                            </tr>
                            <tr>
                                <td>本日の予約件数</td>
                                @forelse ($totalCourseCate as $item)
                                    <td>{{ $item->count_course ? $item->count_course : 0 }}</td>
                                    @php
                                        $countCourseRevenue += $item->total_course_revenue;
                                        $countCourse += $item->count_course;
                                    @endphp
                                @empty
                                @endforelse
                                <td>{{ $countCourse }}</td>
                                <td>{{ number_format($countCourseRevenue - $totalPriceCast, 1) }} 円</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <hr class="dashed">
                <div class="row">
                    <div class="col-md-6">
                        <div class="table-info-course-working">
                            <table class="table table-hover table-bordered table-shadown text-center">
                                <thead>
                                    <th colspan="2">現在のコース別件数</th>
                                </thead>
                                <tbody>
                                    @forelse ($totalCourseCateWorking as $item)
                                        <tr>
                                            <td>{{ $item->name }}</td>
                                            <td class="col-count-course">
                                                {{ $item->count_course ? $item->count_course : 0 }} 件
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="2" class="color-red">※該当なデータ無し </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                        <div class="table-info-order-minute-extension">
                            <table class="table table-hover table-bordered table-shadown text-center">
                                <tbody>
                                    <tr>
                                        <td>延長</td>
                                        <td class="col-extension">{{ $totalOrderExtension->count_extension }} 件</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="table-info-order-type">
                            <table class="table table-hover table-bordered table-shadown text-center">
                                <thead>
                                    <th colspan="2">現在の種別件数</th>
                                </thead>
                                <tbody>
                                    @foreach ($arrLabelTypeFee as $key => $label)
                                        @php
                                            $totalFee = 0;
                                            $strTitle = '';
                                            if ($totalOrderByTypeFee && isset($totalOrderByTypeFee[$key])) {
                                                $totalFee = $totalOrderByTypeFee[$key]['count_type_fee'];
                                            }
                                            if ($key == $modelOrder::TYPE_NET) {
                                                $arrLabelNet = $modelOrder->getLabelMethod();
                                                foreach ($arrLabelNet as $keyLabel => $labelNet) {
                                                    if (isset($totalMethodNetFee[$keyLabel])) {
                                                        $strTitle .= '<br>' . $labelNet . ': ' . $totalMethodNetFee[$keyLabel];
                                                    } else {
                                                        $strTitle .= '<br>' . $labelNet . ': 0';
                                                    }
                                                }
                                            }
                                        @endphp
                                        <tr
                                            @if ($key == $modelOrder::TYPE_NET) data-html="true" data-toggle="tooltip" data-placement="top" title="{{ trim($strTitle, '<br>') }}" @endif>
                                            <td class="w-50">{{ $label }}</td>
                                            <td>
                                                {{ $totalFee }} 件
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <hr class="dashed">
                <div class="table-list table-responsive">
                    <table class="table table-hover table-shadown text-center">
                        <thead>
                            <tr>
                                <th colspan="11" class="title-table">本日の出勤キャスト一覧</th>
                            </tr>
                            <tr class="th-shadown">
                                <th>フラグ</th>
                                <th>キャスト名</th>
                                <th>出勤時間</th>
                                <th>退勤時間 </th>
                                @forelse ($arrLabelTypeFee as $key => $label)
                                    <th class="col-type-fee">{{ $label }}</th>
                                @empty
                                @endforelse
                                <th>キャスト入出金</th>
                                <th>出勤日数</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (isset($castCalendars) && $castCalendars)
                                @forelse ($castCalendars as $item)
                                    <tr>
                                        <td col-status>{{ $item->status_text }}</td>
                                        <td col-name><a
                                                href="{{ route('admin.cast_calendar.edit', ['id' => $item->id, 'date' => $date->toDateString()]) }}">{{ $item->cast_name }}</a>
                                        </td>
                                        <td col-date-start>
                                            @if ($item->date_start)
                                                {{ sprintf('%02d',date('H', strtotime($item->date_start)) < BUSINESS_HOURS? date('H', strtotime($item->date_start)) + 24: date('H', strtotime($item->date_start))) .':' .sprintf('%02d', date('i', strtotime($item->date_start))) }}
                                                <br>
                                            @else
                                                ----/--/--
                                            @endif
                                        </td>
                                        <td col-date-end>
                                            @if ($item->date_end)
                                                {{ sprintf('%02d',date('H', strtotime($item->date_end)) < BUSINESS_HOURS? date('H', strtotime($item->date_end)) + 24: date('H', strtotime($item->date_end))) .':' .sprintf('%02d', date('i', strtotime($item->date_end))) }}
                                                <br>
                                            @else
                                                ----/--/--
                                            @endif
                                        </td>
                                        @forelse ($arrLabelTypeFee as $key => $label)
                                            @php
                                                $priceFee = 0;
                                                if (isset($totalTypeFeeCast[$item->cast_id])) {
                                                    if (isset($totalTypeFeeCast[$item->cast_id][$key])) {
                                                        $priceFee += $totalTypeFeeCast[$item->cast_id][$key]->count_type_fee;
                                                    }
                                                }
                                            @endphp
                                            <td>
                                                {{ $priceFee }}
                                            </td>
                                        @empty
                                        @endforelse
                                        <td>{{ number_format($item->price_cast - ($item->price_cast * $item->expenses) / 100 - $item->dormitory_price, 1) }}
                                            円</td>
                                        <td>{{ $item->count_date ? $item->count_date : 0 }} 日</td>
                                    </tr>
                                @empty
                                    <td colspan="11" class="color-red">※該当なデータ無し </td>
                                @endforelse
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        var dateNowPicker = @json($date->toDateString());
        const NO_HAPPEN_YET = @json($modelOrder::STATUS_NO_HAPPEN_YET);
        const START = @json($modelOrder::STATUS_HAPPENING);
        const EXTENSION = @json($modelOrder::STATUS_EXTENSION);
        const FINSH = @json($modelOrder::STATUS_FINISH);
        var castIds = @json($castIds);
        var minuteExtension = @json($minuteExtension);
        var priceExtension = @json($priceExtension);
        var priceCastExtension = @json($priceCastExtension);
    </script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="{{ assetMix('js/page_admin_revenue.js') }} "></script>
@endsection
