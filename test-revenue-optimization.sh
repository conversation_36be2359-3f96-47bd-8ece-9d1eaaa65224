#!/bin/bash

# Test Revenue Optimization Script
# This script tests the revenue optimization using Docker

echo "🚀 Testing Revenue Optimization"
echo "================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Function to run commands in Docker
run_in_docker() {
    docker-compose exec php72 php artisan "$@"
}

echo "📊 Testing Monthly Revenue Optimization..."
echo "Current Month:"
run_in_docker test:revenue-optimization --type=month

echo ""
echo "📊 Testing Previous Month (May 2025):"
run_in_docker test:revenue-optimization --type=month --month=5 --year=2025

echo ""
echo "📊 Testing Yearly Revenue Optimization..."
echo "Current Year:"
run_in_docker test:revenue-optimization --type=year

echo ""
echo "📊 Testing Previous Year (2024):"
run_in_docker test:revenue-optimization --type=year --year=2024

echo ""
echo "✅ Revenue optimization testing completed!"
echo ""
echo "💡 To run individual tests:"
echo "   Monthly: docker-compose exec php72 php artisan test:revenue-optimization --type=month --month=5 --year=2025"
echo "   Yearly:  docker-compose exec php72 php artisan test:revenue-optimization --type=year --year=2024"
echo ""
echo "💡 To migrate daily revenue data:"
echo "   docker-compose exec php72 php artisan migrate:daily-revenue --month=5 --year=2025"
