<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDailyRevenuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('daily_revenues', function (Blueprint $table) {
            $table->id();
            $table->date('date')->unique();
            
            // Order statistics
            $table->integer('total_orders_count')->default(0);
            $table->decimal('total_revenue', 15, 2)->default(0);
            $table->decimal('total_cash_revenue', 15, 2)->default(0);
            $table->decimal('total_card_revenue', 15, 2)->default(0);
            $table->decimal('total_cast_payments', 15, 2)->default(0);
            
            // Cast calendar statistics
            $table->decimal('total_dormitory_price', 15, 2)->default(0);
            $table->decimal('total_expenses', 15, 2)->default(0);
            $table->decimal('price_for_cast', 15, 2)->default(0);
            
            // Other revenue sources
            $table->decimal('total_living_price', 15, 2)->default(0);
            $table->decimal('total_decorate_room_price', 15, 2)->default(0);
            $table->decimal('total_cost_shop', 15, 2)->default(0);
            
            // Customer statistics
            $table->integer('total_customers')->default(0);
            
            // Card payment details
            $table->integer('card_orders_count')->default(0);
            $table->decimal('card_orders_total_price', 15, 2)->default(0);
            $table->decimal('card_orders_tax_price', 15, 2)->default(0);
            $table->decimal('card_orders_cast_price', 15, 2)->default(0);
            
            // Card payment breakdown by day
            $table->integer('card_orders_in_day15_count')->default(0);
            $table->decimal('card_orders_in_day15_total_price', 15, 2)->default(0);
            $table->decimal('card_orders_in_day15_tax_price', 15, 2)->default(0);
            $table->decimal('card_orders_in_day15_cast_price', 15, 2)->default(0);
            
            $table->integer('card_orders_last_month_count')->default(0);
            $table->decimal('card_orders_last_month_total_price', 15, 2)->default(0);
            $table->decimal('card_orders_last_month_tax_price', 15, 2)->default(0);
            $table->decimal('card_orders_last_month_cast_price', 15, 2)->default(0);
            
            // Cash payment details
            $table->decimal('cash_orders_total_price', 15, 2)->default(0);
            $table->decimal('cash_orders_cast_not_support', 15, 2)->default(0);
            $table->decimal('cash_orders_support_price', 15, 2)->default(0);
            
            // Revenue breakdown by course (JSON)
            $table->json('revenue_by_course')->nullable();
            
            // Revenue breakdown by type fee (JSON)
            $table->json('revenue_by_type_fee')->nullable();
            
            // Calculated totals
            $table->decimal('net_revenue', 15, 2)->default(0);
            $table->decimal('gross_profit', 15, 2)->default(0);
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index('date');
            $table->index(['date', 'total_revenue']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('daily_revenues');
    }
}
